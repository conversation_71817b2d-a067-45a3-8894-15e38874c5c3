/**
 * Ocean Soul Sparkles Admin - VAPID Key Generator
 * Generates VAPID keys for push notifications
 */

const webpush = require('web-push');
const fs = require('fs');
const path = require('path');

console.log('🔑 Generating VAPID keys for Ocean Soul Sparkles Admin...\n');

try {
  // Generate VAPID keys
  const vapidKeys = webpush.generateVAPIDKeys();
  
  console.log('✅ VAPID keys generated successfully!\n');
  console.log('📋 Add these to your .env.local file:\n');
  console.log('# Push Notification VAPID Keys');
  console.log(`NEXT_PUBLIC_VAPID_PUBLIC_KEY=${vapidKeys.publicKey}`);
  console.log(`VAPID_PRIVATE_KEY=${vapidKeys.privateKey}`);
  console.log(`VAPID_SUBJECT=mailto:<EMAIL>\n`);
  
  // Save to a file for reference
  const envContent = `
# Push Notification VAPID Keys - Generated ${new Date().toISOString()}
NEXT_PUBLIC_VAPID_PUBLIC_KEY=${vapidKeys.publicKey}
VAPID_PRIVATE_KEY=${vapidKeys.privateKey}
VAPID_SUBJECT=mailto:<EMAIL>
`;

  const outputPath = path.join(__dirname, '..', 'vapid-keys.env');
  fs.writeFileSync(outputPath, envContent.trim());
  
  console.log(`💾 Keys saved to: ${outputPath}`);
  console.log('⚠️  Remember to add these to your .env.local file and delete vapid-keys.env for security!');
  
} catch (error) {
  console.error('❌ Failed to generate VAPID keys:', error);
  process.exit(1);
}
