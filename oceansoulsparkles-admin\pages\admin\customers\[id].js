import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { useAuth } from '@/hooks/useAuth';
import AdminLayout from '@/components/admin/AdminLayout';
import styles from '@/styles/admin/CustomerDetails.module.css';

export default function CustomerDetails() {
  const router = useRouter();
  const { id } = router.query;
  const { user, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);
  const [customer, setCustomer] = useState(null);
  const [bookings, setBookings] = useState([]);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (id && !authLoading && user) {
      loadCustomer();
      loadCustomerBookings();
    }
  }, [id, authLoading, user]);

  const loadCustomer = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('admin-token');
      const response = await fetch(`/api/admin/customers/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch customer details');
      }

      const data = await response.json();
      setCustomer(data.customer);
    } catch (error) {
      console.error('Error loading customer:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const loadCustomerBookings = async () => {
    try {
      const token = localStorage.getItem('admin-token');
      const response = await fetch(`/api/admin/customers/${id}/bookings`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setBookings(data.bookings || []);
      }
    } catch (error) {
      console.error('Error loading customer bookings:', error);
    }
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this customer? This action cannot be undone.')) {
      return;
    }

    try {
      const token = localStorage.getItem('admin-token');
      const response = await fetch(`/api/admin/customers/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to delete customer');
      }

      router.push('/admin/customers');
    } catch (error) {
      console.error('Error deleting customer:', error);
      alert('Failed to delete customer: ' + error.message);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-AU');
  };

  const formatDateTime = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString('en-AU');
  };

  const getBookingStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'confirmed': return '#28a745';
      case 'pending': return '#ffc107';
      case 'cancelled': return '#dc3545';
      case 'completed': return '#17a2b8';
      default: return '#6c757d';
    }
  };

  if (authLoading || loading) {
    return (
      <AdminLayout>
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading customer details...</p>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className={styles.errorContainer}>
          <h2>Error Loading Customer</h2>
          <p>{error}</p>
          <Link href="/admin/customers" className={styles.backButton}>
            ← Back to Customers
          </Link>
        </div>
      </AdminLayout>
    );
  }

  if (!customer) {
    return (
      <AdminLayout>
        <div className={styles.notFoundContainer}>
          <h2>Customer Not Found</h2>
          <p>The customer you're looking for doesn't exist.</p>
          <Link href="/admin/customers" className={styles.backButton}>
            ← Back to Customers
          </Link>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Head>
        <title>{customer.first_name} {customer.last_name} - Customer Details | Ocean Soul Sparkles Admin</title>
        <meta name="description" content={`Details for customer ${customer.first_name} ${customer.last_name}`} />
      </Head>

      <div className={styles.customerDetailsContainer}>
        <header className={styles.header}>
          <div className={styles.breadcrumb}>
            <Link href="/admin/customers">Customers</Link>
            <span>/</span>
            <span>{customer.first_name} {customer.last_name}</span>
          </div>
          
          <div className={styles.headerActions}>
            <Link href={`/admin/customers/${customer.id}/edit`} className={styles.editButton}>
              ✏️ Edit Customer
            </Link>
            <Link href={`/admin/bookings/new?customer=${customer.id}`} className={styles.bookButton}>
              📅 New Booking
            </Link>
            <button onClick={handleDelete} className={styles.deleteButton}>
              🗑️ Delete
            </button>
            <Link href="/admin/customers" className={styles.backButton}>
              ← Back to Customers
            </Link>
          </div>
        </header>

        <div className={styles.customerContent}>
          <div className={styles.mainInfo}>
            <div className={styles.customerHeader}>
              <div className={styles.customerAvatar}>
                {customer.first_name?.[0]}{customer.last_name?.[0]}
              </div>
              <div className={styles.customerName}>
                <h1>{customer.first_name} {customer.last_name}</h1>
                <p className={styles.customerEmail}>{customer.email}</p>
              </div>
            </div>

            <div className={styles.detailsGrid}>
              <div className={styles.detailCard}>
                <h4>Contact Information</h4>
                <div className={styles.contactInfo}>
                  <div className={styles.contactItem}>
                    <strong>Email:</strong> {customer.email || 'N/A'}
                  </div>
                  <div className={styles.contactItem}>
                    <strong>Phone:</strong> {customer.phone || 'N/A'}
                  </div>
                  {customer.phone_secondary && (
                    <div className={styles.contactItem}>
                      <strong>Secondary Phone:</strong> {customer.phone_secondary}
                    </div>
                  )}
                  <div className={styles.contactItem}>
                    <strong>Address:</strong> {customer.address || 'N/A'}
                  </div>
                </div>
              </div>

              <div className={styles.detailCard}>
                <h4>Personal Information</h4>
                <div className={styles.personalInfo}>
                  <div className={styles.infoItem}>
                    <strong>Date of Birth:</strong> {formatDate(customer.date_of_birth)}
                  </div>
                  <div className={styles.infoItem}>
                    <strong>Customer Since:</strong> {formatDate(customer.created_at)}
                  </div>
                  <div className={styles.infoItem}>
                    <strong>Total Bookings:</strong> {customer.total_bookings || 0}
                  </div>
                </div>
              </div>

              {customer.notes && (
                <div className={styles.detailCard}>
                  <h4>Notes</h4>
                  <p className={styles.notes}>{customer.notes}</p>
                </div>
              )}
            </div>

            <div className={styles.metaInfo}>
              <div className={styles.metaItem}>
                <strong>Created:</strong> {formatDateTime(customer.created_at)}
              </div>
              <div className={styles.metaItem}>
                <strong>Last Updated:</strong> {formatDateTime(customer.updated_at)}
              </div>
              <div className={styles.metaItem}>
                <strong>Customer ID:</strong> {customer.id}
              </div>
            </div>
          </div>

          <div className={styles.sidebar}>
            <div className={styles.quickActions}>
              <h3>Quick Actions</h3>
              <Link href={`/admin/customers/${customer.id}/edit`} className={styles.actionButton}>
                Edit Customer Details
              </Link>
              <Link href={`/admin/bookings/new?customer=${customer.id}`} className={styles.actionButton}>
                Create New Booking
              </Link>
              <Link href={`/admin/customers/${customer.id}/history`} className={styles.actionButton}>
                View Full History
              </Link>
            </div>

            <div className={styles.bookingHistory}>
              <h3>Recent Bookings</h3>
              {bookings.length === 0 ? (
                <p className={styles.noBookings}>No bookings found</p>
              ) : (
                <div className={styles.bookingsList}>
                  {bookings.slice(0, 5).map(booking => (
                    <div key={booking.id} className={styles.bookingItem}>
                      <div className={styles.bookingHeader}>
                        <span className={styles.serviceName}>{booking.service_name}</span>
                        <span 
                          className={styles.bookingStatus}
                          style={{ backgroundColor: getBookingStatusColor(booking.status) }}
                        >
                          {booking.status}
                        </span>
                      </div>
                      <div className={styles.bookingDetails}>
                        <div>{formatDate(booking.booking_date)}</div>
                        <div>{booking.booking_time}</div>
                        <div>${booking.total_amount}</div>
                      </div>
                    </div>
                  ))}
                  {bookings.length > 5 && (
                    <Link href={`/admin/customers/${customer.id}/history`} className={styles.viewAllBookings}>
                      View All {bookings.length} Bookings →
                    </Link>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
