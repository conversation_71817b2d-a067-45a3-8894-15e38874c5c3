/**
 * Ocean Soul Sparkles Admin - Keyboard Shortcuts Help
 * Modal component showing available keyboard shortcuts
 */

import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import styles from '../../styles/admin/KeyboardShortcuts.module.css';

interface KeyboardShortcut {
  keys: string[];
  description: string;
  category: string;
}

interface KeyboardShortcutsProps {
  isOpen: boolean;
  onClose: () => void;
}

const SHORTCUTS: KeyboardShortcut[] = [
  // Navigation
  { keys: ['Ctrl', 'K'], description: 'Open global search', category: 'Navigation' },
  { keys: ['Ctrl', 'H'], description: 'Go to dashboard', category: 'Navigation' },
  { keys: ['Ctrl', 'U'], description: 'Go to customers', category: 'Navigation' },
  { keys: ['Ctrl', 'B'], description: 'Go to bookings', category: 'Navigation' },
  { keys: ['Ctrl', 'S'], description: 'Go to services', category: 'Navigation' },
  { keys: ['Ctrl', 'P'], description: 'Go to products', category: 'Navigation' },
  { keys: ['Ctrl', 'I'], description: 'Go to inventory', category: 'Navigation' },
  { keys: ['Ctrl', 'T'], description: 'Go to staff management', category: 'Navigation' },

  // Actions
  { keys: ['Ctrl', 'N'], description: 'Create new item', category: 'Actions' },
  { keys: ['Ctrl', 'E'], description: 'Export current data', category: 'Actions' },
  { keys: ['Ctrl', 'A'], description: 'Select all items', category: 'Actions' },
  { keys: ['Delete'], description: 'Delete selected items', category: 'Actions' },
  { keys: ['Ctrl', 'Z'], description: 'Undo last action', category: 'Actions' },
  { keys: ['Ctrl', 'Y'], description: 'Redo last action', category: 'Actions' },

  // Interface
  { keys: ['Escape'], description: 'Close modal/dropdown', category: 'Interface' },
  { keys: ['Tab'], description: 'Navigate between elements', category: 'Interface' },
  { keys: ['Shift', 'Tab'], description: 'Navigate backwards', category: 'Interface' },
  { keys: ['Enter'], description: 'Confirm action', category: 'Interface' },
  { keys: ['Space'], description: 'Toggle selection', category: 'Interface' },

  // Search & Filters
  { keys: ['Ctrl', 'F'], description: 'Focus search field', category: 'Search & Filters' },
  { keys: ['Ctrl', 'L'], description: 'Clear all filters', category: 'Search & Filters' },
  { keys: ['↑', '↓'], description: 'Navigate search results', category: 'Search & Filters' },

  // Help
  { keys: ['Ctrl', '?'], description: 'Show keyboard shortcuts', category: 'Help' },
  { keys: ['F1'], description: 'Open help documentation', category: 'Help' }
];

export default function KeyboardShortcuts({ isOpen, onClose }: KeyboardShortcutsProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose]);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  if (!mounted || !isOpen) return null;

  const categories = Array.from(new Set(SHORTCUTS.map(s => s.category)));

  const formatKey = (key: string) => {
    const keyMap: { [key: string]: string } = {
      'Ctrl': '⌘',
      'Alt': '⌥',
      'Shift': '⇧',
      'Enter': '↵',
      'Space': '␣',
      'Tab': '⇥',
      'Escape': '⎋',
      'Delete': '⌫',
      '↑': '↑',
      '↓': '↓',
      '←': '←',
      '→': '→'
    };

    return keyMap[key] || key;
  };

  const modal = (
    <div className={styles.overlay} onClick={onClose}>
      <div className={styles.modal} onClick={(e) => e.stopPropagation()}>
        <header className={styles.header}>
          <h2 className={styles.title}>Keyboard Shortcuts</h2>
          <button
            onClick={onClose}
            className={styles.closeBtn}
            aria-label="Close shortcuts help"
          >
            ✕
          </button>
        </header>

        <div className={styles.content}>
          <div className={styles.intro}>
            <p>Use these keyboard shortcuts to navigate and interact with the admin dashboard more efficiently.</p>
          </div>

          <div className={styles.shortcutsGrid}>
            {categories.map(category => (
              <div key={category} className={styles.category}>
                <h3 className={styles.categoryTitle}>{category}</h3>
                <div className={styles.shortcutsList}>
                  {SHORTCUTS
                    .filter(shortcut => shortcut.category === category)
                    .map((shortcut, index) => (
                      <div key={index} className={styles.shortcutItem}>
                        <div className={styles.keys}>
                          {shortcut.keys.map((key, keyIndex) => (
                            <React.Fragment key={keyIndex}>
                              <kbd className={styles.key}>{formatKey(key)}</kbd>
                              {keyIndex < shortcut.keys.length - 1 && (
                                <span className={styles.keySeparator}>+</span>
                              )}
                            </React.Fragment>
                          ))}
                        </div>
                        <div className={styles.description}>
                          {shortcut.description}
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            ))}
          </div>

          <div className={styles.footer}>
            <div className={styles.tip}>
              <strong>💡 Tip:</strong> Most shortcuts work globally throughout the admin dashboard.
              Press <kbd className={styles.key}>Ctrl</kbd> + <kbd className={styles.key}>?</kbd> anytime to open this help.
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return createPortal(modal, document.body);
}

// Hook for managing keyboard shortcuts
export function useKeyboardShortcuts() {
  const [showShortcuts, setShowShortcuts] = useState(false);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ignore shortcuts when typing in input fields
      if (
        event.target instanceof HTMLInputElement ||
        event.target instanceof HTMLTextAreaElement ||
        event.target instanceof HTMLSelectElement ||
        (event.target as HTMLElement)?.contentEditable === 'true'
      ) {
        return;
      }

      // Handle global shortcuts
      if (event.ctrlKey || event.metaKey) {
        switch (event.key.toLowerCase()) {
          case '?':
            event.preventDefault();
            setShowShortcuts(true);
            break;
          case 'k':
            event.preventDefault();
            // Trigger global search
            const searchInput = document.querySelector('[data-global-search]') as HTMLInputElement;
            if (searchInput) {
              searchInput.focus();
            }
            break;
          case 'h':
            event.preventDefault();
            window.location.href = '/admin';
            break;
          case 'u':
            event.preventDefault();
            window.location.href = '/admin/customers';
            break;
          case 'b':
            event.preventDefault();
            window.location.href = '/admin/bookings';
            break;
          case 's':
            event.preventDefault();
            window.location.href = '/admin/services';
            break;
          case 'p':
            event.preventDefault();
            window.location.href = '/admin/products';
            break;
          case 'i':
            event.preventDefault();
            window.location.href = '/admin/inventory';
            break;
          case 't':
            event.preventDefault();
            window.location.href = '/admin/staff';
            break;
          case 'f':
            event.preventDefault();
            // Focus search field
            const searchField = document.querySelector('input[type="search"], input[placeholder*="search" i]') as HTMLInputElement;
            if (searchField) {
              searchField.focus();
            }
            break;
        }
      }

      // Handle other shortcuts
      switch (event.key) {
        case 'F1':
          event.preventDefault();
          setShowShortcuts(true);
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  return {
    showShortcuts,
    setShowShortcuts,
    openShortcuts: () => setShowShortcuts(true),
    closeShortcuts: () => setShowShortcuts(false)
  };
}

// Component to add keyboard shortcuts help button to any page
export function KeyboardShortcutsButton({ className = '' }: { className?: string }) {
  const { showShortcuts, openShortcuts, closeShortcuts } = useKeyboardShortcuts();

  return (
    <>
      <button
        onClick={openShortcuts}
        className={`${styles.helpButton} ${className}`}
        title="Keyboard shortcuts (Ctrl + ?)"
        aria-label="Show keyboard shortcuts help"
      >
        ⌨️
      </button>
      <KeyboardShortcuts isOpen={showShortcuts} onClose={closeShortcuts} />
    </>
  );
}
