Stack trace:
Frame         Function      Args
0007FFFF9DF0  00021005FE8E (000210285F68, 00021026AB6E, 00000067AFD0, 0007FFFF8CF0) msys-2.0.dll+0x1FE8E
0007FFFF9DF0  0002100467F9 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x67F9
0007FFFF9DF0  000210046832 (000210286019, 0000054F249C, 00000067AFD0, 000000000000) msys-2.0.dll+0x6832
0007FFFF9DF0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9DF0  000210068E24 (0007FFFF9E00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA0D0  00021006A225 (0007FFFF9E00, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA80820000 ntdll.dll
7FFA7D2C0000 xtajit64.dll
7FFA7DC10000 KERNEL32.DLL
7FFA7C450000 KERNELBASE.dll
7FFA7B540000 apphelp.dll
7FFA7D430000 USER32.dll
7FFA7BCF0000 win32u.dll
7FFA7CD50000 GDI32.dll
7FFA7BD60000 gdi32full.dll
7FFA7C1A0000 msvcp_win.dll
7FFA7BF70000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA7FEF0000 advapi32.dll
7FFA7D0A0000 msvcrt.dll
7FFA7DAD0000 sechost.dll
7FFA80040000 RPCRT4.dll
7FFA7A4C0000 CRYPTBASE.DLL
7FFA7CAF0000 bcryptPrimitives.dll
7FFA7CCD0000 IMM32.DLL
