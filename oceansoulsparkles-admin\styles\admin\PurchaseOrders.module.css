/* Purchase Orders Styles */

.purchaseOrders {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 2px solid #e2e8f0;
}

.headerContent {
  flex: 1;
}

.title {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
  font-family: 'Inter', sans-serif;
}

.subtitle {
  font-size: 1rem;
  color: #64748b;
  margin: 0;
}

.headerActions {
  display: flex;
  gap: 1rem;
}

.addBtn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.addBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* Controls */
.controls {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 2rem;
  align-items: center;
}

.searchSection {
  flex: 1;
}

.searchInput {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.searchInput:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filters {
  display: flex;
  gap: 1rem;
}

.filterSelect,
.sortSelect {
  padding: 0.75rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filterSelect:focus,
.sortSelect:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Orders Container */
.ordersContainer {
  min-height: 400px;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  color: #64748b;
}

.loadingSpinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.emptyState {
  text-align: center;
  padding: 4rem 2rem;
  color: #64748b;
}

.emptyIcon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.emptyState h3 {
  font-size: 1.5rem;
  color: #374151;
  margin-bottom: 0.5rem;
}

.emptyState p {
  font-size: 1rem;
  margin-bottom: 2rem;
}

.addFirstBtn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  display: inline-block;
  transition: all 0.3s ease;
}

.addFirstBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* Orders Grid */
.ordersGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
}

.orderCard {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.orderCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.cardHeader {
  margin-bottom: 1rem;
}

.orderInfo {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
}

.poNumber {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  flex: 1;
}

.statusBadge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.statusDraft {
  background: #f3f4f6;
  color: #374151;
}

.statusSent {
  background: #dbeafe;
  color: #1e40af;
}

.statusConfirmed {
  background: #fef3c7;
  color: #92400e;
}

.statusReceived {
  background: #dcfce7;
  color: #166534;
}

.statusCancelled {
  background: #fee2e2;
  color: #991b1b;
}

.cardBody {
  margin-bottom: 1.5rem;
}

.supplierInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: #f8fafc;
  border-radius: 8px;
}

.orderDetails {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
  margin: 1rem 0;
}

.detailItem {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.label {
  font-size: 0.75rem;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.value {
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
}

.notes {
  margin-top: 1rem;
  padding: 1rem;
  background: #f1f5f9;
  border-radius: 8px;
}

.notesText {
  font-size: 0.875rem;
  color: #475569;
  margin: 0.5rem 0 0 0;
  line-height: 1.5;
}

.cardFooter {
  border-top: 1px solid #e2e8f0;
  padding-top: 1rem;
}

.cardActions {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.viewBtn,
.editBtn,
.receiveBtn {
  flex: 1;
  min-width: 80px;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  text-align: center;
  transition: all 0.3s ease;
}

.viewBtn {
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #e2e8f0;
}

.viewBtn:hover {
  background: #e2e8f0;
  color: #334155;
}

.editBtn {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fbbf24;
}

.editBtn:hover {
  background: #fde68a;
  color: #78350f;
}

.receiveBtn {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #22c55e;
}

.receiveBtn:hover {
  background: #bbf7d0;
  color: #14532d;
}

.deleteBtn {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  background: #fee2e2;
  color: #991b1b;
  border: 1px solid #fca5a5;
  cursor: pointer;
  transition: all 0.3s ease;
}

.deleteBtn:hover {
  background: #fecaca;
  color: #7f1d1d;
}

.cardMeta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.createdBy,
.createdDate {
  font-size: 0.75rem;
  color: #9ca3af;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .ordersGrid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .purchaseOrders {
    padding: 1rem;
  }

  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .controls {
    flex-direction: column;
    gap: 1rem;
  }

  .filters {
    flex-direction: column;
    gap: 0.75rem;
  }

  .ordersGrid {
    grid-template-columns: 1fr;
  }

  .orderDetails {
    grid-template-columns: 1fr;
  }

  .cardActions {
    flex-direction: column;
  }

  .orderInfo {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .cardMeta {
    flex-direction: column;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 1.5rem;
  }

  .orderCard {
    padding: 1rem;
  }

  .supplierInfo {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}

/* Purchase Order Form Styles */
.purchaseOrderForm {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.formSection {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.formSection h3 {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 1.5rem 0;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.addItemBtn {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.addItemBtn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 10px rgba(34, 197, 94, 0.3);
}

.formGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.formGroup.fullWidth {
  grid-column: 1 / -1;
}

.formLabel {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

.formInput,
.formTextarea,
.formSelect {
  padding: 0.75rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.formInput:focus,
.formTextarea:focus,
.formSelect:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.formTextarea {
  resize: vertical;
  min-height: 80px;
}

.emptyItems {
  text-align: center;
  padding: 3rem 2rem;
  color: #64748b;
  background: #f8fafc;
  border-radius: 8px;
  border: 2px dashed #e2e8f0;
}

.itemsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.itemRow {
  display: flex;
  align-items: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.itemFields {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 1rem;
  flex: 1;
}

.totalCost {
  padding: 0.75rem 1rem;
  background: #f1f5f9;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-weight: 600;
  color: #1e293b;
}

.removeItemBtn {
  background: #fee2e2;
  color: #991b1b;
  border: 1px solid #fca5a5;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.25rem;
  font-weight: bold;
  transition: all 0.3s ease;
}

.removeItemBtn:hover {
  background: #fecaca;
  color: #7f1d1d;
}

.orderSummary {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.orderSummary h3 {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 1.5rem 0;
}

.summaryRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #e2e8f0;
}

.summaryRow:last-child {
  border-bottom: none;
}

.totalRow {
  font-size: 1.125rem;
  font-weight: 700;
  color: #1e293b;
  border-top: 2px solid #e2e8f0;
  margin-top: 0.5rem;
  padding-top: 1rem;
}

.formActions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding: 2rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.saveBtn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.saveBtn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.saveBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.cancelBtn {
  background: #f8fafc;
  color: #64748b;
  border: 2px solid #e2e8f0;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancelBtn:hover {
  background: #f1f5f9;
  color: #475569;
}

/* Form Responsive Design */
@media (max-width: 768px) {
  .purchaseOrderForm {
    padding: 1rem;
  }

  .formGrid {
    grid-template-columns: 1fr;
  }

  .itemFields {
    grid-template-columns: 1fr;
  }

  .itemRow {
    flex-direction: column;
    align-items: stretch;
  }

  .removeItemBtn {
    align-self: flex-end;
  }

  .formActions {
    flex-direction: column;
  }

  .sectionHeader {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
}
