/**
 * Ocean Soul Sparkles Admin - Export Button Component
 * Reusable export button with dropdown for different formats
 */

import React, { useState, useRef, useEffect } from 'react';
import { 
  exportBookings, 
  exportServices, 
  exportCustomers, 
  exportProducts, 
  exportInventory,
  ExportOptions 
} from '../../lib/export/data-export';
import styles from '../../styles/admin/ExportButton.module.css';

export type ExportType = 'bookings' | 'services' | 'customers' | 'products' | 'inventory' | 'custom';

interface ExportButtonProps {
  data: any[];
  type: ExportType;
  className?: string;
  disabled?: boolean;
  options?: ExportOptions;
  onExportStart?: () => void;
  onExportComplete?: () => void;
  onExportError?: (error: Error) => void;
}

export default function ExportButton({
  data,
  type,
  className = '',
  disabled = false,
  options = {},
  onExportStart,
  onExportComplete,
  onExportError
}: ExportButtonProps) {
  const [isExporting, setIsExporting] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleExport = async (format: 'csv' | 'excel' | 'pdf' = 'csv') => {
    if (!data || data.length === 0) {
      onExportError?.(new Error('No data to export'));
      return;
    }

    setIsExporting(true);
    setShowDropdown(false);
    onExportStart?.();

    try {
      // For now, we only support CSV export
      // Excel and PDF can be added later
      if (format === 'csv') {
        switch (type) {
          case 'bookings':
            exportBookings(data, options);
            break;
          case 'services':
            exportServices(data, options);
            break;
          case 'customers':
            exportCustomers(data, options);
            break;
          case 'products':
            exportProducts(data, options);
            break;
          case 'inventory':
            exportInventory(data, options);
            break;
          case 'custom':
            // For custom exports, use the generic export function
            const { exportToCSV } = await import('../../lib/export/data-export');
            exportToCSV(data, options);
            break;
          default:
            throw new Error(`Unsupported export type: ${type}`);
        }
      } else {
        throw new Error(`Export format ${format} not yet supported`);
      }

      onExportComplete?.();
    } catch (error) {
      console.error('Export error:', error);
      onExportError?.(error instanceof Error ? error : new Error('Export failed'));
    } finally {
      setIsExporting(false);
    }
  };

  const getButtonText = () => {
    if (isExporting) return 'Exporting...';
    if (data.length === 0) return 'No Data';
    return `📥 Export (${data.length})`;
  };

  const getButtonTitle = () => {
    if (disabled || data.length === 0) return 'No data available to export';
    return `Export ${data.length} ${type} records`;
  };

  return (
    <div className={`${styles.exportButton} ${className}`} ref={dropdownRef}>
      <button
        onClick={() => setShowDropdown(!showDropdown)}
        disabled={disabled || isExporting || data.length === 0}
        className={`${styles.exportBtn} ${isExporting ? styles.exporting : ''}`}
        title={getButtonTitle()}
      >
        {isExporting && <div className={styles.spinner}></div>}
        {getButtonText()}
        {!isExporting && data.length > 0 && (
          <span className={styles.dropdownArrow}>▼</span>
        )}
      </button>

      {showDropdown && data.length > 0 && (
        <div className={styles.exportDropdown}>
          <div className={styles.dropdownHeader}>
            <span>Export Format</span>
          </div>
          
          <button
            onClick={() => handleExport('csv')}
            className={styles.dropdownItem}
            disabled={isExporting}
          >
            <span className={styles.formatIcon}>📄</span>
            <div className={styles.formatInfo}>
              <div className={styles.formatName}>CSV File</div>
              <div className={styles.formatDesc}>Comma-separated values</div>
            </div>
          </button>

          <button
            onClick={() => handleExport('excel')}
            className={`${styles.dropdownItem} ${styles.disabled}`}
            disabled={true}
            title="Excel export coming soon"
          >
            <span className={styles.formatIcon}>📊</span>
            <div className={styles.formatInfo}>
              <div className={styles.formatName}>Excel File</div>
              <div className={styles.formatDesc}>Coming soon</div>
            </div>
          </button>

          <button
            onClick={() => handleExport('pdf')}
            className={`${styles.dropdownItem} ${styles.disabled}`}
            disabled={true}
            title="PDF export coming soon"
          >
            <span className={styles.formatIcon}>📋</span>
            <div className={styles.formatInfo}>
              <div className={styles.formatName}>PDF Report</div>
              <div className={styles.formatDesc}>Coming soon</div>
            </div>
          </button>
        </div>
      )}
    </div>
  );
}

// Quick export functions for simple use cases
export const QuickExportButton = ({ 
  data, 
  type, 
  className = '',
  ...props 
}: Omit<ExportButtonProps, 'onExportStart' | 'onExportComplete' | 'onExportError'>) => {
  const handleExportError = (error: Error) => {
    console.error('Export error:', error);
    alert(`Export failed: ${error.message}`);
  };

  return (
    <ExportButton
      data={data}
      type={type}
      className={className}
      onExportError={handleExportError}
      {...props}
    />
  );
};
