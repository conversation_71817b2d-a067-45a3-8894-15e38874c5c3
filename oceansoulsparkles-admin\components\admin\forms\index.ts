/**
 * Ocean Soul Sparkles Admin Dashboard - Mobile Forms Index
 * Export all mobile form components for easy importing
 */

// Input components
export { default as MobileInput } from './MobileInput';
export type { MobileInputProps } from './MobileInput';
export {
  MobileEmailInput,
  MobilePhoneInput,
  MobileNumberInput,
  MobileSearchInput,
  MobilePasswordInput
} from './MobileInput';

// Select components
export { default as MobileSelect } from './MobileSelect';
export type { MobileSelectProps, SelectOption } from './MobileSelect';

// Validation components
export { default as MobileValidation } from './MobileValidation';
export type { 
  MobileValidationProps, 
  ValidationRule, 
  FormValidationState 
} from './MobileValidation';
export {
  MobileSuccess,
  MobileWarning,
  validateField,
  useMobileFormValidation
} from './MobileValidation';

// Form wrapper components
export { default as MobileForm } from './MobileForm';
export type { MobileFormProps } from './MobileForm';
export {
  MobileFormField,
  MobileFormSection
} from './MobileForm';
