/* Receipts Page Styles */
.receiptsPage {
  padding: 24px;
  width: 100%;
  min-height: calc(100vh - 120px);
  background: #f8fafc;
}

.header {
  margin-bottom: 32px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.headerContent h1 {
  margin: 0 0 8px 0;
  color: #1e293b;
  font-size: 2rem;
  font-weight: 700;
}

.headerContent p {
  margin: 0;
  color: #64748b;
  font-size: 1.1rem;
  line-height: 1.6;
}

/* Message Styles */
.message {
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 24px;
  font-weight: 500;
}

.message.success {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.message.error {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

/* Tab Navigation */
.tabNavigation {
  display: flex;
  gap: 4px;
  margin-bottom: 24px;
  border-bottom: 1px solid #e2e8f0;
}

.tabButton {
  background: none;
  border: none;
  padding: 12px 20px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  color: #64748b;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tabButton:hover {
  color: #475569;
  background: #f8fafc;
}

.tabButton.active {
  color: #667eea;
  border-bottom-color: #667eea;
  background: #f8fafc;
}

/* Tab Content */
.tabContent {
  min-height: 600px;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tabHeader {
  margin-bottom: 24px;
}

.tabHeader h2 {
  margin: 0 0 8px 0;
  color: #1e293b;
  font-size: 1.5rem;
  font-weight: 600;
}

.tabHeader p {
  margin: 0;
  color: #64748b;
  font-size: 1rem;
  line-height: 1.5;
}

/* Templates Tab */
.templatesTab {
  /* Styles handled by ReceiptCustomizer component */
  background: transparent;
  padding: 0;
  box-shadow: none;
}

/* Settings Tab */
.settingsTab {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.receiptSettings {
  max-width: 1000px;
}

.settingsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
  margin-bottom: 32px;
}

.settingsSection {
  background: #f8fafc;
  padding: 24px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.settingsSection h3 {
  margin: 0 0 20px 0;
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 2px solid #667eea;
}

.settingGroup {
  margin-bottom: 20px;
}

.settingGroup:last-child {
  margin-bottom: 0;
}

.settingGroup label {
  display: block;
  margin-bottom: 6px;
  color: #374151;
  font-weight: 500;
  font-size: 0.95rem;
}

.settingGroup input[type="text"],
.settingGroup input[type="email"],
.settingGroup select,
.settingGroup textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.95rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  background: white;
}

.settingGroup input[type="text"]:focus,
.settingGroup input[type="email"]:focus,
.settingGroup select:focus,
.settingGroup textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.settingGroup textarea {
  resize: vertical;
  min-height: 80px;
}

.checkboxLabel {
  display: flex !important;
  align-items: flex-start;
  gap: 10px;
  cursor: pointer;
  margin-bottom: 0 !important;
}

.checkboxLabel input[type="checkbox"] {
  width: auto !important;
  margin: 0;
  flex-shrink: 0;
  margin-top: 2px;
}

.checkboxLabel span {
  color: #374151;
  font-weight: 400;
  line-height: 1.5;
}

/* Settings Actions */
.settingsActions {
  display: flex;
  justify-content: flex-end;
  padding-top: 24px;
  border-top: 1px solid #e2e8f0;
}

.saveButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.saveButton:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.saveButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Loading State */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px;
  color: #64748b;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .receiptsPage {
    padding: 20px;
  }
  
  .settingsGrid {
    grid-template-columns: 1fr;
    gap: 24px;
  }
}

@media (max-width: 768px) {
  .receiptsPage {
    padding: 16px;
  }
  
  .header {
    margin-bottom: 24px;
  }
  
  .headerContent h1 {
    font-size: 1.75rem;
  }
  
  .headerContent p {
    font-size: 1rem;
  }
  
  .tabNavigation {
    flex-wrap: wrap;
  }
  
  .tabButton {
    padding: 10px 16px;
    font-size: 0.9rem;
  }
  
  .settingsTab {
    padding: 20px;
  }
  
  .settingsSection {
    padding: 20px;
  }
  
  .settingsActions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .saveButton {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .receiptsPage {
    padding: 12px;
  }
  
  .headerContent h1 {
    font-size: 1.5rem;
  }
  
  .tabButton {
    padding: 8px 12px;
    font-size: 0.85rem;
  }
  
  .settingsTab {
    padding: 16px;
  }
  
  .settingsSection {
    padding: 16px;
  }
  
  .settingGroup input[type="text"],
  .settingGroup input[type="email"],
  .settingGroup select,
  .settingGroup textarea {
    padding: 8px 10px;
    font-size: 0.9rem;
  }
}
