#!/usr/bin/env node

/**
 * Test script to verify booking management fixes
 * Tests the three critical issues that were identified:
 * 1. Service dropdown loading
 * 2. Artist dropdown loading  
 * 3. Booking creation functionality
 */

import fetch from 'node-fetch';

const API_BASE_URL = 'http://localhost:3002/api';

// Test credentials (using dev account from database setup)
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'DevPassword123!'
};

async function testBookingFixes() {
  console.log('🧪 Testing Booking Management Fixes');
  console.log('=====================================\n');

  let authToken = null;

  try {
    // Step 1: Authenticate
    console.log('1. Authenticating...');
    const authResponse = await fetch(`${API_BASE_URL}/admin/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(TEST_CREDENTIALS)
    });

    if (!authResponse.ok) {
      throw new Error(`Authentication failed: ${authResponse.status}`);
    }

    const authData = await authResponse.json();
    authToken = authData.token;
    console.log('✅ Authentication successful\n');

    // Step 2: Test Services API (Issue #1)
    console.log('2. Testing Services API...');
    const servicesResponse = await fetch(`${API_BASE_URL}/admin/services`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (!servicesResponse.ok) {
      throw new Error(`Services API failed: ${servicesResponse.status}`);
    }

    const servicesData = await servicesResponse.json();
    console.log(`✅ Services loaded: ${servicesData.services?.length || 0} services found`);
    
    if (servicesData.services && servicesData.services.length > 0) {
      const sampleService = servicesData.services[0];
      console.log(`   Sample: ${sampleService.name} - $${sampleService.base_price || sampleService.price}`);
    }
    console.log('');

    // Step 3: Test Artists API (Issue #2)
    console.log('3. Testing Artists API...');
    const artistsResponse = await fetch(`${API_BASE_URL}/admin/artists`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (!artistsResponse.ok) {
      throw new Error(`Artists API failed: ${artistsResponse.status}`);
    }

    const artistsData = await artistsResponse.json();
    console.log(`✅ Artists loaded: ${artistsData.artists?.length || 0} artists found`);
    
    if (artistsData.artists && artistsData.artists.length > 0) {
      const sampleArtist = artistsData.artists[0];
      console.log(`   Sample: ${sampleArtist.name || sampleArtist.artist_name} - ${sampleArtist.specializations?.join(', ') || 'No specializations'}`);
    }
    console.log('');

    // Step 4: Test Service Tiers API
    if (servicesData.services && servicesData.services.length > 0) {
      const testServiceId = servicesData.services[0].id;
      console.log('4. Testing Service Tiers API...');
      
      const tiersResponse = await fetch(`${API_BASE_URL}/admin/services/${testServiceId}/tiers`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (tiersResponse.ok) {
        const tiersData = await tiersResponse.json();
        console.log(`✅ Service tiers loaded: ${tiersData.tiers?.length || 0} tiers found`);
        
        if (tiersData.tiers && tiersData.tiers.length > 0) {
          const sampleTier = tiersData.tiers[0];
          console.log(`   Sample: ${sampleTier.name} - $${sampleTier.price} (${sampleTier.duration}min)`);
        }
      } else {
        console.log(`⚠️  Service tiers API returned: ${tiersResponse.status}`);
      }
      console.log('');
    }

    // Step 5: Test Customers API (needed for booking creation)
    console.log('5. Testing Customers API...');
    const customersResponse = await fetch(`${API_BASE_URL}/admin/customers`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    let customersData = null;
    if (customersResponse.ok) {
      customersData = await customersResponse.json();
      console.log(`✅ Customers loaded: ${customersData.customers?.length || 0} customers found`);
    } else {
      console.log(`⚠️  Customers API returned: ${customersResponse.status}`);
    }
    console.log('');

    // Step 6: Test Booking Creation API (Issue #3)
    console.log('6. Testing Booking Creation API...');
    
    // Check if we have the required data for booking creation
    const hasServices = servicesData.services && servicesData.services.length > 0;
    const hasArtists = artistsData.artists && artistsData.artists.length > 0;
    const hasCustomers = customersData && customersData.customers && customersData.customers.length > 0;

    if (hasServices && hasArtists && hasCustomers) {
      // Create a test booking
      const testBooking = {
        customer_id: customersData.customers[0].id,
        service_id: servicesData.services[0].id,
        assigned_artist_id: artistsData.artists[0].id,
        start_time: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
        end_time: new Date(Date.now() + 24 * 60 * 60 * 1000 + 60 * 60 * 1000).toISOString(), // Tomorrow + 1 hour
        status: 'confirmed',
        total_amount: 50.00,
        notes: 'Test booking created by automated test',
        location: 'Studio',
        booking_source: 'admin'
      };

      const bookingResponse = await fetch(`${API_BASE_URL}/admin/bookings`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testBooking)
      });

      if (bookingResponse.ok) {
        const bookingData = await bookingResponse.json();
        console.log('✅ Booking creation successful');
        console.log(`   Booking ID: ${bookingData.booking?.id}`);
        console.log(`   Customer: ${customersData.customers[0].first_name} ${customersData.customers[0].last_name}`);
        console.log(`   Service: ${servicesData.services[0].name}`);
        console.log(`   Artist: ${artistsData.artists[0].name || artistsData.artists[0].artist_name}`);
      } else {
        const errorData = await bookingResponse.json();
        console.log(`❌ Booking creation failed: ${bookingResponse.status}`);
        console.log(`   Error: ${errorData.error || errorData.message}`);
      }
    } else {
      console.log('⚠️  Cannot test booking creation - missing required data:');
      console.log(`   Services: ${hasServices ? '✅' : '❌'}`);
      console.log(`   Artists: ${hasArtists ? '✅' : '❌'}`);
      console.log(`   Customers: ${hasCustomers ? '✅' : '❌'}`);
    }

    console.log('\n🎉 Test completed successfully!');
    console.log('\n📋 SUMMARY OF FIXES APPLIED:');
    console.log('=====================================');
    console.log('✅ Fixed Services API column mapping:');
    console.log('   - Changed base_price → price');
    console.log('   - Changed is_active → status');
    console.log('   - Added proper data transformation');
    console.log('');
    console.log('✅ Fixed Artists API column mapping:');
    console.log('   - Removed non-existent columns (name, email, phone)');
    console.log('   - Used correct columns (artist_name, display_name, etc.)');
    console.log('   - Added proper data transformation');
    console.log('');
    console.log('✅ Fixed Authentication Headers:');
    console.log('   - Added Bearer token to all API calls');
    console.log('   - Fixed loadInitialData function');
    console.log('   - Fixed booking creation API call');
    console.log('');
    console.log('✅ Fixed Frontend Compatibility:');
    console.log('   - Service dropdown now uses price || base_price');
    console.log('   - Artist dropdown uses name || artist_name');
    console.log('   - Proper error handling and logging');
    console.log('');
    console.log('🔍 EXPECTED RESULTS:');
    console.log('- Service dropdown: Should show 17 services from database');
    console.log('- Artist dropdown: Should show 5 artists from database');
    console.log('- Create booking button: Should work and create bookings');
    console.log('');
    console.log('🌐 Test the UI at: http://localhost:3002/admin/bookings/new');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testBookingFixes();
