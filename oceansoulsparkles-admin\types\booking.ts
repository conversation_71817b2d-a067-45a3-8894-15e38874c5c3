/**
 * Ocean Soul Sparkles Admin Dashboard - Booking Types
 * Booking management and scheduling types
 */

import { 
  ID, 
  Timestamp, 
  BookingStatus, 
  PaymentStatus,
  Money,
  TimeSlot,
  CustomField,
  MediaFile
} from './common';

// Core booking interface
export interface Booking {
  id: ID;
  
  // Customer and service details
  customerId: ID;
  serviceId: ID;
  artistId: ID;
  assignedArtistId?: ID; // For reassignments
  
  // Scheduling
  bookingDate: string; // ISO date
  startTime: string; // HH:MM format
  endTime: string; // HH:MM format
  duration: number; // Minutes
  timezone: string;
  
  // Service details
  serviceName: string;
  serviceDescription?: string;
  tierName?: string;
  tierPrice?: Money;
  
  // Location and setup
  location: 'studio' | 'mobile' | 'client_home';
  locationDetails?: string;
  setupTime?: number; // Minutes before start
  cleanupTime?: number; // Minutes after end
  
  // Pricing and payments
  basePrice: Money;
  totalAmount: Money;
  discountAmount?: Money;
  discountReason?: string;
  taxAmount?: Money;
  tipAmount?: Money;
  
  // Status and workflow
  status: BookingStatus;
  paymentStatus: PaymentStatus;
  confirmationCode?: string;
  
  // Communication
  notes?: string;
  internalNotes?: string;
  specialRequirements?: string[];
  clientInstructions?: string;
  
  // Booking source and attribution
  bookingSource: 'online' | 'phone' | 'walk_in' | 'admin' | 'mobile_app';
  referralSource?: string;
  marketingChannel?: string;
  
  // Reminders and notifications
  remindersSent: Array<{
    type: 'email' | 'sms' | 'push';
    sentAt: Timestamp;
    status: 'sent' | 'delivered' | 'failed';
  }>;
  
  // Attachments and media
  attachments?: MediaFile[];
  beforePhotos?: MediaFile[];
  afterPhotos?: MediaFile[];
  
  // Custom fields and metadata
  customFields?: CustomField[];
  metadata?: Record<string, any>;
  
  // Cancellation and changes
  cancellationReason?: string;
  cancellationFee?: Money;
  cancelledAt?: Timestamp;
  cancelledBy?: ID;
  
  // Rescheduling history
  originalBookingDate?: string;
  originalStartTime?: string;
  rescheduleCount: number;
  rescheduleHistory?: Array<{
    fromDate: string;
    fromTime: string;
    toDate: string;
    toTime: string;
    reason?: string;
    rescheduledAt: Timestamp;
    rescheduledBy: ID;
  }>;
  
  // System fields
  createdAt: Timestamp;
  updatedAt: Timestamp;
  createdBy?: ID;
  updatedBy?: ID;
}

// Booking creation and update types
export interface CreateBookingData {
  customerId: ID;
  serviceId: ID;
  artistId: ID;
  bookingDate: string;
  startTime: string;
  endTime: string;
  location?: Booking['location'];
  locationDetails?: string;
  tierName?: string;
  tierPrice?: Money;
  notes?: string;
  specialRequirements?: string[];
  clientInstructions?: string;
  bookingSource?: Booking['bookingSource'];
  referralSource?: string;
  customFields?: CustomField[];
}

export interface UpdateBookingData extends Partial<CreateBookingData> {
  id: ID;
  status?: BookingStatus;
  paymentStatus?: PaymentStatus;
  totalAmount?: Money;
  discountAmount?: Money;
  discountReason?: string;
  tipAmount?: Money;
  internalNotes?: string;
  cancellationReason?: string;
  cancellationFee?: Money;
}

// Booking search and filtering
export interface BookingSearchParams {
  query?: string;
  customerId?: ID;
  artistId?: ID;
  serviceId?: ID;
  status?: BookingStatus[];
  paymentStatus?: PaymentStatus[];
  location?: Booking['location'][];
  bookingSource?: Booking['bookingSource'][];
  dateFrom?: string;
  dateTo?: string;
  timeFrom?: string;
  timeTo?: string;
  amountMin?: number;
  amountMax?: number;
  createdFrom?: string;
  createdTo?: string;
}

export interface BookingFilters {
  search: string;
  status: BookingStatus | 'all';
  paymentStatus: PaymentStatus | 'all';
  artist: ID | 'all';
  service: ID | 'all';
  location: Booking['location'] | 'all';
  dateRange: {
    start?: string;
    end?: string;
  } | null;
  amountRange: {
    min?: number;
    max?: number;
  } | null;
}

// Calendar and scheduling types
export interface CalendarEvent {
  id: ID;
  bookingId: ID;
  title: string;
  start: string; // ISO datetime
  end: string; // ISO datetime
  allDay: boolean;
  backgroundColor: string;
  borderColor: string;
  textColor: string;
  extendedProps: {
    booking: Booking;
    customer: {
      id: ID;
      name: string;
      phone?: string;
      email?: string;
    };
    service: {
      id: ID;
      name: string;
      duration: number;
    };
    artist: {
      id: ID;
      name: string;
    };
  };
}

export interface TimeSlotAvailability {
  date: string; // ISO date
  artistId: ID;
  slots: Array<{
    startTime: string; // HH:MM
    endTime: string; // HH:MM
    available: boolean;
    reason?: string; // If not available
    bookingId?: ID; // If booked
  }>;
}

export interface BookingConflict {
  type: 'artist_unavailable' | 'double_booking' | 'insufficient_time' | 'outside_hours';
  message: string;
  conflictingBookingId?: ID;
  suggestedAlternatives?: Array<{
    date: string;
    startTime: string;
    endTime: string;
    artistId: ID;
    artistName: string;
  }>;
}

// Booking analytics and reporting
export interface BookingAnalytics {
  totalBookings: number;
  confirmedBookings: number;
  completedBookings: number;
  cancelledBookings: number;
  noShowBookings: number;
  
  // Revenue metrics
  totalRevenue: Money;
  averageBookingValue: Money;
  revenueByStatus: {
    completed: Money;
    confirmed: Money;
    cancelled: Money;
  };
  
  // Time-based metrics
  bookingsByDay: Array<{
    date: string;
    count: number;
    revenue: Money;
  }>;
  bookingsByHour: Array<{
    hour: number; // 0-23
    count: number;
    revenue: Money;
  }>;
  bookingsByDayOfWeek: Array<{
    dayOfWeek: number; // 0-6
    dayName: string;
    count: number;
    revenue: Money;
  }>;
  
  // Service performance
  topServices: Array<{
    serviceId: ID;
    serviceName: string;
    bookingCount: number;
    revenue: Money;
    averageRating?: number;
  }>;
  
  // Artist performance
  artistPerformance: Array<{
    artistId: ID;
    artistName: string;
    bookingCount: number;
    revenue: Money;
    averageRating?: number;
    utilizationRate: number; // Percentage
  }>;
  
  // Booking sources
  bookingsBySource: Array<{
    source: Booking['bookingSource'];
    count: number;
    percentage: number;
    revenue: Money;
  }>;
  
  // Cancellation analysis
  cancellationAnalysis: {
    cancellationRate: number; // Percentage
    noShowRate: number; // Percentage
    topCancellationReasons: Array<{
      reason: string;
      count: number;
      percentage: number;
    }>;
    cancellationsByTimeframe: Array<{
      timeframe: string; // e.g., "Same day", "1-3 days", "1 week+"
      count: number;
      percentage: number;
    }>;
  };
}

// Booking workflow and automation
export interface BookingWorkflow {
  id: ID;
  name: string;
  description?: string;
  triggers: Array<{
    event: 'booking_created' | 'booking_confirmed' | 'booking_completed' | 'booking_cancelled';
    conditions?: Record<string, any>;
  }>;
  actions: Array<{
    type: 'send_email' | 'send_sms' | 'create_task' | 'update_status' | 'charge_payment';
    config: Record<string, any>;
    delay?: number; // Minutes
  }>;
  isActive: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface BookingReminder {
  id: ID;
  bookingId: ID;
  type: 'email' | 'sms' | 'push';
  scheduledFor: Timestamp;
  sentAt?: Timestamp;
  status: 'scheduled' | 'sent' | 'delivered' | 'failed' | 'cancelled';
  template: string;
  customMessage?: string;
  retryCount: number;
  lastError?: string;
}

// Booking validation and business rules
export interface BookingValidationRules {
  minimumAdvanceBooking: number; // Hours
  maximumAdvanceBooking: number; // Days
  allowSameDayBooking: boolean;
  requireDeposit: boolean;
  depositPercentage?: number;
  cancellationPolicy: {
    freeUntil: number; // Hours before appointment
    partialRefundUntil: number; // Hours before appointment
    partialRefundPercentage: number;
    noRefundAfter: number; // Hours before appointment
  };
  reschedulePolicy: {
    allowedCount: number;
    freeUntil: number; // Hours before appointment
    feeAfter: Money;
  };
}

// Booking templates and packages
export interface BookingTemplate {
  id: ID;
  name: string;
  description?: string;
  serviceId: ID;
  duration: number;
  defaultPrice: Money;
  requiredFields: string[];
  optionalFields: string[];
  instructions?: string;
  isActive: boolean;
}

export interface BookingPackage {
  id: ID;
  name: string;
  description?: string;
  services: Array<{
    serviceId: ID;
    quantity: number;
    discountPercentage?: number;
  }>;
  totalSessions: number;
  validityPeriod: number; // Days
  packagePrice: Money;
  savings: Money;
  isActive: boolean;
}

// Booking notifications and communications
export interface BookingNotification {
  id: ID;
  bookingId: ID;
  recipientType: 'customer' | 'artist' | 'admin';
  recipientId: ID;
  type: 'confirmation' | 'reminder' | 'cancellation' | 'reschedule' | 'completion';
  channel: 'email' | 'sms' | 'push' | 'in_app';
  subject?: string;
  message: string;
  scheduledFor?: Timestamp;
  sentAt?: Timestamp;
  deliveredAt?: Timestamp;
  readAt?: Timestamp;
  status: 'scheduled' | 'sent' | 'delivered' | 'read' | 'failed';
  errorMessage?: string;
}
