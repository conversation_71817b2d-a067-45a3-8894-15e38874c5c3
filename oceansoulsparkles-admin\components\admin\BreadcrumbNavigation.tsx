/**
 * Ocean Soul Sparkles Admin - Dynamic Breadcrumb Navigation Component
 * Automatically generates breadcrumb navigation based on current route
 */

import React, { useMemo } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useBreadcrumbData, getDynamicBreadcrumbLabel } from '../../hooks/useBreadcrumbData';
import styles from '../../styles/admin/BreadcrumbNavigation.module.css';

interface BreadcrumbItem {
  label: string;
  href: string;
  icon?: string;
  isActive?: boolean;
}

interface BreadcrumbNavigationProps {
  className?: string;
  showIcons?: boolean;
  maxItems?: number;
}

// Route configuration for breadcrumb generation
const ROUTE_CONFIG: Record<string, { label: string; icon?: string; parent?: string }> = {
  '/admin': { label: 'Admin', icon: '🏠' },
  '/admin/dashboard': { label: 'Dashboard', icon: '📊', parent: '/admin' },
  
  // Core Features
  '/admin/services': { label: 'Services', icon: '✨', parent: '/admin/dashboard' },
  '/admin/services/new': { label: 'New Service', icon: '➕', parent: '/admin/services' },
  '/admin/services/[id]': { label: 'Service Details', icon: '📝', parent: '/admin/services' },
  
  '/admin/products': { label: 'Products', icon: '🛍️', parent: '/admin/dashboard' },
  '/admin/products/new': { label: 'New Product', icon: '➕', parent: '/admin/products' },
  '/admin/products/[id]': { label: 'Product Details', icon: '📝', parent: '/admin/products' },
  
  '/admin/inventory': { label: 'Inventory', icon: '📦', parent: '/admin/dashboard' },
  '/admin/inventory/new': { label: 'New Item', icon: '➕', parent: '/admin/inventory' },
  
  '/admin/pos': { label: 'POS Terminal', icon: '💳', parent: '/admin/dashboard' },
  
  // Customer Management
  '/admin/customers': { label: 'Customers', icon: '👥', parent: '/admin/dashboard' },
  '/admin/customers/new': { label: 'New Customer', icon: '➕', parent: '/admin/customers' },
  '/admin/customers/[id]': { label: 'Customer Details', icon: '👤', parent: '/admin/customers' },
  
  // Booking Management
  '/admin/bookings': { label: 'Bookings', icon: '📅', parent: '/admin/dashboard' },
  '/admin/bookings/new': { label: 'New Booking', icon: '➕', parent: '/admin/bookings' },
  '/admin/bookings/[id]': { label: 'Booking Details', icon: '📝', parent: '/admin/bookings' },
  
  // Staff & Artists
  '/admin/staff': { label: 'Staff Management', icon: '👨‍💼', parent: '/admin/dashboard' },
  '/admin/staff/onboarding': { label: 'Staff Onboarding', icon: '🎯', parent: '/admin/staff' },
  '/admin/staff/training': { label: 'Training', icon: '📚', parent: '/admin/staff' },
  '/admin/staff/performance': { label: 'Performance', icon: '📈', parent: '/admin/staff' },
  
  '/admin/artists': { label: 'Artists', icon: '🎨', parent: '/admin/dashboard' },
  '/admin/artists/portfolio': { label: 'Portfolio', icon: '🖼️', parent: '/admin/artists' },
  '/admin/artists/[id]': { label: 'Artist Details', icon: '👨‍🎨', parent: '/admin/artists' },
  
  // Communications
  '/admin/communications': { label: 'Communications', icon: '📧', parent: '/admin/dashboard' },
  '/admin/email-templates': { label: 'Email Templates', icon: '📧', parent: '/admin/communications' },
  '/admin/sms-templates': { label: 'SMS Templates', icon: '📱', parent: '/admin/communications' },
  '/admin/notifications': { label: 'Notifications', icon: '🔔', parent: '/admin/communications' },
  
  // Analytics & Reports
  '/admin/reports': { label: 'Reports & Analytics', icon: '📊', parent: '/admin/dashboard' },
  '/admin/receipts': { label: 'Receipt Templates', icon: '🧾', parent: '/admin/reports' },
  '/admin/feedback': { label: 'Customer Feedback', icon: '💬', parent: '/admin/reports' },
  
  // Suppliers & Purchase Orders
  '/admin/suppliers': { label: 'Suppliers', icon: '🏢', parent: '/admin/inventory' },
  '/admin/suppliers/new': { label: 'New Supplier', icon: '➕', parent: '/admin/suppliers' },
  '/admin/purchase-orders': { label: 'Purchase Orders', icon: '📋', parent: '/admin/inventory' },
  '/admin/purchase-orders/new': { label: 'New Order', icon: '➕', parent: '/admin/purchase-orders' },
  
  // System
  '/admin/settings': { label: 'Settings', icon: '⚙️', parent: '/admin/dashboard' },
  '/admin/tips': { label: 'Tips Management', icon: '💰', parent: '/admin/settings' },
  
  // Testing & Debug
  '/admin/mobile-debug': { label: 'Mobile Debug', icon: '🔧', parent: '/admin/settings' },
  '/admin/mobile-test': { label: 'Mobile Test', icon: '📱', parent: '/admin/settings' },
};

export default function BreadcrumbNavigation({
  className = '',
  showIcons = true,
  maxItems = 5
}: BreadcrumbNavigationProps) {
  const router = useRouter();
  const breadcrumbData = useBreadcrumbData();

  const breadcrumbs = useMemo(() => {
    const currentPath = router.asPath.split('?')[0]; // Remove query parameters
    const pathSegments = currentPath.split('/').filter(Boolean);
    
    // Build breadcrumb items
    const items: BreadcrumbItem[] = [];
    
    // Always start with Dashboard for admin routes
    if (currentPath.startsWith('/admin') && currentPath !== '/admin/dashboard') {
      items.push({
        label: 'Dashboard',
        href: '/admin/dashboard',
        icon: '📊'
      });
    }
    
    // Generate breadcrumbs based on current path
    const generateBreadcrumbsForPath = (path: string): BreadcrumbItem[] => {
      const config = ROUTE_CONFIG[path];
      if (!config) {
        // Handle dynamic routes
        const dynamicPath = path.replace(/\/[^/]+$/, '/[id]');
        const dynamicConfig = ROUTE_CONFIG[dynamicPath];
        if (dynamicConfig) {
          const breadcrumbs: BreadcrumbItem[] = [];
          if (dynamicConfig.parent) {
            breadcrumbs.push(...generateBreadcrumbsForPath(dynamicConfig.parent));
          }
          
          // Get the actual item name for dynamic routes
          const segments = path.split('/');
          const id = segments[segments.length - 1];
          let itemLabel = dynamicConfig.label;

          // Try to get dynamic label from breadcrumb data
          const dynamicLabel = getDynamicBreadcrumbLabel(path, breadcrumbData);
          if (dynamicLabel) {
            itemLabel = dynamicLabel;
          } else if (id !== 'new') {
            // Fallback to ID-based labels
            if (path.includes('/customers/')) {
              itemLabel = `Customer #${id.slice(0, 8)}`;
            } else if (path.includes('/bookings/')) {
              itemLabel = `Booking #${id.slice(0, 8)}`;
            } else if (path.includes('/services/')) {
              itemLabel = `Service #${id.slice(0, 8)}`;
            } else if (path.includes('/products/')) {
              itemLabel = `Product #${id.slice(0, 8)}`;
            } else if (path.includes('/artists/')) {
              itemLabel = `Artist #${id.slice(0, 8)}`;
            }
          }
          
          breadcrumbs.push({
            label: itemLabel,
            href: path,
            icon: dynamicConfig.icon
          });
          
          return breadcrumbs;
        }
        
        // Fallback for unknown routes
        const segments = path.split('/').filter(Boolean);
        return [{
          label: segments[segments.length - 1].charAt(0).toUpperCase() + segments[segments.length - 1].slice(1),
          href: path,
          icon: '📄'
        }];
      }
      
      const breadcrumbs: BreadcrumbItem[] = [];
      if (config.parent) {
        breadcrumbs.push(...generateBreadcrumbsForPath(config.parent));
      }
      
      breadcrumbs.push({
        label: config.label,
        href: path,
        icon: config.icon
      });
      
      return breadcrumbs;
    };
    
    const pathBreadcrumbs = generateBreadcrumbsForPath(currentPath);
    
    // Merge with dashboard breadcrumb, avoiding duplicates
    const allBreadcrumbs = [...items];
    pathBreadcrumbs.forEach(breadcrumb => {
      if (!allBreadcrumbs.some(item => item.href === breadcrumb.href)) {
        allBreadcrumbs.push(breadcrumb);
      }
    });
    
    // Mark the last item as active
    if (allBreadcrumbs.length > 0) {
      allBreadcrumbs[allBreadcrumbs.length - 1].isActive = true;
    }
    
    // Limit the number of items if specified
    if (maxItems && allBreadcrumbs.length > maxItems) {
      const truncated = [
        allBreadcrumbs[0], // Always keep the first item
        { label: '...', href: '', icon: '⋯' }, // Ellipsis
        ...allBreadcrumbs.slice(-2) // Keep the last 2 items
      ];
      return truncated;
    }
    
    return allBreadcrumbs;
  }, [router.asPath, maxItems]);

  if (breadcrumbs.length <= 1) {
    return null; // Don't show breadcrumbs for single items
  }

  return (
    <nav className={`${styles.breadcrumbNavigation} ${className}`} aria-label="Breadcrumb">
      <ol className={styles.breadcrumbList}>
        {breadcrumbs.map((item, index) => (
          <li key={`${item.href}-${index}`} className={styles.breadcrumbItem}>
            {item.href && !item.isActive ? (
              <Link href={item.href} className={styles.breadcrumbLink}>
                {showIcons && item.icon && (
                  <span className={styles.breadcrumbIcon}>{item.icon}</span>
                )}
                <span className={styles.breadcrumbLabel}>{item.label}</span>
              </Link>
            ) : (
              <span className={`${styles.breadcrumbCurrent} ${item.isActive ? styles.active : ''}`}>
                {showIcons && item.icon && (
                  <span className={styles.breadcrumbIcon}>{item.icon}</span>
                )}
                <span className={styles.breadcrumbLabel}>{item.label}</span>
              </span>
            )}
            
            {index < breadcrumbs.length - 1 && (
              <span className={styles.breadcrumbSeparator} aria-hidden="true">
                /
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
}
