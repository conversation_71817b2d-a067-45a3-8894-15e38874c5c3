/**
 * Ocean Soul Sparkles Admin Dashboard - Uptime Monitoring
 * Monitors application uptime and health checks
 */

import { recordAPIPerformance } from './performance-monitor';
import { auditLog } from '../security/audit-logging';

// Health check endpoints to monitor
const HEALTH_CHECK_ENDPOINTS = [
  { url: '/api/health', name: 'API Health' },
  { url: '/api/admin/dashboard', name: 'Dashboard API' },
  { url: '/api/auth/verify', name: 'Auth API' },
  { url: '/admin/login', name: 'Login Page' }
];

// Uptime monitoring configuration
const UPTIME_CONFIG = {
  CHECK_INTERVAL: 5 * 60 * 1000, // 5 minutes
  TIMEOUT: 10000, // 10 seconds
  MAX_RETRIES: 3,
  ALERT_THRESHOLD: 3 // Alert after 3 consecutive failures
};

interface UptimeCheck {
  endpoint: string;
  name: string;
  status: 'up' | 'down' | 'degraded';
  responseTime: number;
  statusCode?: number;
  error?: string;
  timestamp: string;
}

interface UptimeStats {
  uptime: number; // percentage
  totalChecks: number;
  successfulChecks: number;
  averageResponseTime: number;
  lastCheck: string;
  status: 'healthy' | 'degraded' | 'down';
}

// In-memory storage for uptime data (in production, use Redis or database)
let uptimeHistory: UptimeCheck[] = [];
let consecutiveFailures: { [endpoint: string]: number } = {};

/**
 * Perform health check on a single endpoint
 */
async function performHealthCheck(endpoint: { url: string; name: string }): Promise<UptimeCheck> {
  const startTime = Date.now();
  const timestamp = new Date().toISOString();

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), UPTIME_CONFIG.TIMEOUT);

    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3002';
    const fullUrl = `${baseUrl}${endpoint.url}`;

    const response = await fetch(fullUrl, {
      method: 'GET',
      signal: controller.signal,
      headers: {
        'User-Agent': 'Ocean-Soul-Sparkles-Uptime-Monitor/1.0'
      }
    });

    clearTimeout(timeoutId);
    const responseTime = Date.now() - startTime;

    const check: UptimeCheck = {
      endpoint: endpoint.url,
      name: endpoint.name,
      status: response.ok ? 'up' : 'degraded',
      responseTime,
      statusCode: response.status,
      timestamp
    };

    // Reset consecutive failures on success
    if (response.ok) {
      consecutiveFailures[endpoint.url] = 0;
    } else {
      consecutiveFailures[endpoint.url] = (consecutiveFailures[endpoint.url] || 0) + 1;
      check.error = `HTTP ${response.status}`;
    }

    // Record performance metric
    await recordAPIPerformance(
      endpoint.url,
      'GET',
      responseTime,
      response.status,
      undefined, // No user for uptime checks
      'uptime-monitor',
      'Ocean-Soul-Sparkles-Uptime-Monitor/1.0'
    );

    return check;

  } catch (error) {
    const responseTime = Date.now() - startTime;
    consecutiveFailures[endpoint.url] = (consecutiveFailures[endpoint.url] || 0) + 1;

    const check: UptimeCheck = {
      endpoint: endpoint.url,
      name: endpoint.name,
      status: 'down',
      responseTime,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp
    };

    // Record failed performance metric
    await recordAPIPerformance(
      endpoint.url,
      'GET',
      responseTime,
      0, // No status code for network errors
      undefined,
      'uptime-monitor',
      'Ocean-Soul-Sparkles-Uptime-Monitor/1.0',
      check.error
    );

    return check;
  }
}

/**
 * Perform health checks on all monitored endpoints
 */
export async function performAllHealthChecks(): Promise<UptimeCheck[]> {
  console.log('[UPTIME] Starting health checks...');
  
  const checks: UptimeCheck[] = [];

  for (const endpoint of HEALTH_CHECK_ENDPOINTS) {
    try {
      const check = await performHealthCheck(endpoint);
      checks.push(check);

      // Log check result
      const status = check.status === 'up' ? '✅' : check.status === 'degraded' ? '⚠️' : '❌';
      console.log(`[UPTIME] ${status} ${check.name}: ${check.status} (${check.responseTime}ms)`);

      // Check for alerts
      await checkUptimeAlerts(check);

    } catch (error) {
      console.error(`[UPTIME] Error checking ${endpoint.name}:`, error);
    }
  }

  // Store checks in history (keep last 1000 checks)
  uptimeHistory.push(...checks);
  if (uptimeHistory.length > 1000) {
    uptimeHistory = uptimeHistory.slice(-1000);
  }

  return checks;
}

/**
 * Check for uptime alerts and send notifications
 */
async function checkUptimeAlerts(check: UptimeCheck): Promise<void> {
  const failures = consecutiveFailures[check.endpoint] || 0;

  if (failures >= UPTIME_CONFIG.ALERT_THRESHOLD) {
    const severity = check.status === 'down' ? 'critical' : 'high';
    
    await auditLog({
      action: 'UPTIME_ALERT',
      path: check.endpoint,
      severity,
      metadata: {
        name: check.name,
        status: check.status,
        consecutiveFailures: failures,
        responseTime: check.responseTime,
        error: check.error
      }
    });

    console.error(`[UPTIME_ALERT] ${severity.toUpperCase()}: ${check.name} has been ${check.status} for ${failures} consecutive checks`);
  }
}

/**
 * Get uptime statistics for an endpoint
 */
export function getUptimeStats(endpoint?: string): UptimeStats {
  const relevantChecks = endpoint 
    ? uptimeHistory.filter(check => check.endpoint === endpoint)
    : uptimeHistory;

  if (relevantChecks.length === 0) {
    return {
      uptime: 0,
      totalChecks: 0,
      successfulChecks: 0,
      averageResponseTime: 0,
      lastCheck: new Date().toISOString(),
      status: 'healthy'
    };
  }

  const totalChecks = relevantChecks.length;
  const successfulChecks = relevantChecks.filter(check => check.status === 'up').length;
  const uptime = (successfulChecks / totalChecks) * 100;
  const averageResponseTime = relevantChecks.reduce((sum, check) => sum + check.responseTime, 0) / totalChecks;
  const lastCheck = relevantChecks[relevantChecks.length - 1];

  let status: 'healthy' | 'degraded' | 'down' = 'healthy';
  if (uptime < 95) {
    status = 'down';
  } else if (uptime < 99) {
    status = 'degraded';
  }

  return {
    uptime: Math.round(uptime * 100) / 100,
    totalChecks,
    successfulChecks,
    averageResponseTime: Math.round(averageResponseTime),
    lastCheck: lastCheck.timestamp,
    status
  };
}

/**
 * Get recent uptime checks
 */
export function getRecentUptimeChecks(limit: number = 50): UptimeCheck[] {
  return uptimeHistory.slice(-limit).reverse();
}

/**
 * Get uptime summary for all endpoints
 */
export function getUptimeSummary(): { [endpoint: string]: UptimeStats } {
  const summary: { [endpoint: string]: UptimeStats } = {};

  for (const endpoint of HEALTH_CHECK_ENDPOINTS) {
    summary[endpoint.url] = getUptimeStats(endpoint.url);
  }

  return summary;
}

/**
 * Start uptime monitoring (call this on app startup)
 */
export function startUptimeMonitoring(): void {
  // Perform initial health check
  performAllHealthChecks();

  // Schedule regular health checks
  setInterval(performAllHealthChecks, UPTIME_CONFIG.CHECK_INTERVAL);

  console.log(`[UPTIME] Uptime monitoring started - checking every ${UPTIME_CONFIG.CHECK_INTERVAL / 1000 / 60} minutes`);
}

/**
 * Create health check API endpoint
 */
export function createHealthCheckResponse(): {
  status: string;
  timestamp: string;
  uptime: number;
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  endpoints: { [key: string]: UptimeStats };
} {
  const memoryUsage = process.memoryUsage();
  const memoryUsedMB = Math.round(memoryUsage.heapUsed / 1024 / 1024);
  const memoryTotalMB = Math.round(memoryUsage.heapTotal / 1024 / 1024);
  const memoryPercentage = Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100);

  const overallStats = getUptimeStats();
  const endpointStats = getUptimeSummary();

  return {
    status: overallStats.status,
    timestamp: new Date().toISOString(),
    uptime: overallStats.uptime,
    memory: {
      used: memoryUsedMB,
      total: memoryTotalMB,
      percentage: memoryPercentage
    },
    endpoints: endpointStats
  };
}
