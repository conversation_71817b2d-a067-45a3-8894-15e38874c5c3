import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import Link from 'next/link';
import { useAuth } from '../../../hooks/useAuth';
import AdminLayout from '../../../components/admin/AdminLayout';
import { toast } from 'react-toastify';
import { errorManager } from '../../../lib/error-handling/error-manager';
import styles from '@/styles/admin/NewBooking.module.css';

/**
 * New Booking Creation Page
 *
 * This page provides a comprehensive form interface for creating new customer bookings,
 * including customer selection, service selection, date/time picking, and artist assignment.
 */
export default function NewBooking() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState([]);
  const [services, setServices] = useState([]);
  const [artists, setArtists] = useState([]);
  const [serviceTiers, setServiceTiers] = useState([]);

  // Form state
  const [formData, setFormData] = useState({
    customer_id: '',
    service_id: '',
    tier_id: '',
    artist_id: '',
    booking_date: '',
    start_time: '',
    duration: 60,
    notes: '',
    location: 'Studio'
  });

  // Pre-select customer if coming from customer page
  useEffect(() => {
    if (router.query.customer) {
      setFormData(prev => ({
        ...prev,
        customer_id: router.query.customer
      }));
    }
  }, [router.query.customer]);

  // Load initial data
  useEffect(() => {
    if (user) {
      loadInitialData();
    }
  }, [user]);

  const loadInitialData = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('admin-token');
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      };

      // Load customers, services, and artists in parallel
      const [customersRes, servicesRes, artistsRes] = await Promise.all([
        fetch('/api/admin/customers', { headers }),
        fetch('/api/admin/services', { headers }),
        fetch('/api/admin/artists', { headers })
      ]);

      if (customersRes.ok) {
        const customersData = await customersRes.json();
        setCustomers(customersData.customers || []);
      } else {
        console.error('Failed to load customers:', customersRes.status, customersRes.statusText);
      }

      if (servicesRes.ok) {
        const servicesData = await servicesRes.json();
        setServices(servicesData.services || []);
      } else {
        console.error('Failed to load services:', servicesRes.status, servicesRes.statusText);
      }

      if (artistsRes.ok) {
        const artistsData = await artistsRes.json();
        setArtists(artistsData.artists || []);
      } else {
        console.error('Failed to load artists:', artistsRes.status, artistsRes.statusText);
      }
    } catch (error) {
      console.error('Error loading initial data:', error);
      toast.error('Failed to load form data');
    } finally {
      setLoading(false);
    }
  };

  // Load service tiers when service is selected
  useEffect(() => {
    if (formData.service_id) {
      loadServiceTiers(formData.service_id);
    } else {
      setServiceTiers([]);
      setFormData(prev => ({ ...prev, tier_id: '', duration: 60 }));
    }
  }, [formData.service_id]);

  const loadServiceTiers = async (serviceId) => {
    try {
      const token = localStorage.getItem('admin-token');
      const response = await fetch(`/api/admin/services/${serviceId}/tiers`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      if (response.ok) {
        const data = await response.json();
        setServiceTiers(data.tiers || []);

        // Auto-select default tier if available
        const defaultTier = data.tiers?.find(tier => tier.is_default);
        if (defaultTier) {
          setFormData(prev => ({
            ...prev,
            tier_id: defaultTier.id,
            duration: defaultTier.duration || 60
          }));
        }
      } else {
        console.error('Failed to load service tiers:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('Error loading service tiers:', error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleTierChange = (e) => {
    const tierId = e.target.value;
    const selectedTier = serviceTiers.find(tier => tier.id === tierId);

    setFormData(prev => ({
      ...prev,
      tier_id: tierId,
      duration: selectedTier?.duration || 60
    }));
  };

  const calculateEndTime = () => {
    if (formData.start_time && formData.duration) {
      const [hours, minutes] = formData.start_time.split(':');
      const startDate = new Date();
      startDate.setHours(parseInt(hours), parseInt(minutes), 0, 0);

      const endDate = new Date(startDate.getTime() + (formData.duration * 60000));
      return endDate.toTimeString().slice(0, 5);
    }
    return '';
  };

  const validateForm = () => {
    const required = ['customer_id', 'service_id', 'tier_id', 'artist_id', 'booking_date', 'start_time'];
    const missing = required.filter(field => !formData[field]);

    if (missing.length > 0) {
      toast.error(`Please fill in all required fields: ${missing.join(', ')}`);
      return false;
    }

    // Validate date is not in the past
    const bookingDateTime = new Date(`${formData.booking_date}T${formData.start_time}`);
    if (bookingDateTime < new Date()) {
      toast.error('Booking date and time cannot be in the past');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      const selectedTier = serviceTiers.find(tier => tier.id === formData.tier_id);
      const endTime = calculateEndTime();

      const bookingData = {
        customer_id: formData.customer_id,
        service_id: formData.service_id,
        assigned_artist_id: formData.artist_id,
        start_time: `${formData.booking_date}T${formData.start_time}:00`,
        end_time: `${formData.booking_date}T${endTime}:00`,
        status: 'confirmed',
        total_amount: selectedTier?.price || 0,
        notes: formData.notes,
        location: formData.location,
        tier_name: selectedTier?.name,
        tier_price: selectedTier?.price,
        booking_source: 'admin'
      };

      const token = localStorage.getItem('admin-token');
      const response = await fetch('/api/admin/bookings', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(bookingData),
      });

      if (response.ok) {
        const result = await response.json();
        toast.success('Booking created successfully!');
        router.push(`/admin/bookings/${result.booking.id}`);
      } else {
        const error = await response.json();

        // Use error manager for comprehensive error handling
        const errorDetails = errorManager.handleBookingError(
          { ...error, status: response.status },
          'Booking Creation'
        );

        // Show specific error message to user
        toast.error(errorDetails.userMessage);
      }
    } catch (error) {
      console.error('Error creating booking:', error);

      // Handle network or other unexpected errors
      const errorDetails = errorManager.handleBookingError(
        error,
        'Booking Creation - Network Error'
      );

      toast.error(errorDetails.userMessage);
    } finally {
      setLoading(false);
    }
  };
  if (authLoading) {
    return (
      <AdminLayout>
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading...</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Head>
        <title>New Booking | Ocean Soul Sparkles Admin</title>
        <meta name="description" content="Create a new customer booking" />
      </Head>

      <div className={styles.newBookingContainer}>
        <header className={styles.header}>
          <div className={styles.headerContent}>
            <h1>Create New Booking</h1>
            <p>Schedule a new appointment for a customer</p>
          </div>
          <div className={styles.headerActions}>
            <Link href="/admin/bookings" className={styles.backButton}>
              ← Back to Bookings
            </Link>
          </div>
        </header>

        <form onSubmit={handleSubmit} className={styles.bookingForm}>
          <div className={styles.formGrid}>
            {/* Customer Selection */}
            <div className={styles.formSection}>
              <h3>Customer Information</h3>
              <div className={styles.formGroup}>
                <label htmlFor="customer_id">Customer *</label>
                <select
                  id="customer_id"
                  name="customer_id"
                  value={formData.customer_id}
                  onChange={handleInputChange}
                  required
                  className={styles.formControl}
                >
                  <option value="">Select a customer...</option>
                  {customers.map(customer => (
                    <option key={customer.id} value={customer.id}>
                      {customer.first_name} {customer.last_name} - {customer.email}
                    </option>
                  ))}
                </select>
              </div>
              <div className={styles.formActions}>
                <Link href="/admin/customers/new" className={styles.linkButton}>
                  + Add New Customer
                </Link>
              </div>
            </div>

            {/* Service Selection */}
            <div className={styles.formSection}>
              <h3>Service Details</h3>
              <div className={styles.formGroup}>
                <label htmlFor="service_id">Service *</label>
                <select
                  id="service_id"
                  name="service_id"
                  value={formData.service_id}
                  onChange={handleInputChange}
                  required
                  className={styles.formControl}
                >
                  <option value="">Select a service...</option>
                  {services.map(service => (
                    <option key={service.id} value={service.id}>
                      {service.name} - ${service.base_price || service.price}
                    </option>
                  ))}
                </select>
              </div>

              {serviceTiers.length > 0 && (
                <div className={styles.formGroup}>
                  <label htmlFor="tier_id">Service Tier *</label>
                  <select
                    id="tier_id"
                    name="tier_id"
                    value={formData.tier_id}
                    onChange={handleTierChange}
                    required
                    className={styles.formControl}
                  >
                    <option value="">Select a tier...</option>
                    {serviceTiers.map(tier => (
                      <option key={tier.id} value={tier.id}>
                        {tier.name} - ${tier.price} ({tier.duration} min)
                      </option>
                    ))}
                  </select>
                </div>
              )}
            </div>

            {/* Artist Selection */}
            <div className={styles.formSection}>
              <h3>Artist Assignment</h3>
              <div className={styles.formGroup}>
                <label htmlFor="artist_id">Artist *</label>
                <select
                  id="artist_id"
                  name="artist_id"
                  value={formData.artist_id}
                  onChange={handleInputChange}
                  required
                  className={styles.formControl}
                >
                  <option value="">Select an artist...</option>
                  {artists.map(artist => (
                    <option key={artist.id} value={artist.id}>
                      {artist.name || artist.artist_name} - {artist.specializations?.join(', ')}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Date and Time */}
            <div className={styles.formSection}>
              <h3>Schedule</h3>
              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label htmlFor="booking_date">Date *</label>
                  <input
                    type="date"
                    id="booking_date"
                    name="booking_date"
                    value={formData.booking_date}
                    onChange={handleInputChange}
                    min={new Date().toISOString().split('T')[0]}
                    required
                    className={styles.formControl}
                  />
                </div>
                <div className={styles.formGroup}>
                  <label htmlFor="start_time">Start Time *</label>
                  <input
                    type="time"
                    id="start_time"
                    name="start_time"
                    value={formData.start_time}
                    onChange={handleInputChange}
                    required
                    className={styles.formControl}
                  />
                </div>
              </div>

              {formData.start_time && formData.duration && (
                <div className={styles.timeInfo}>
                  <p>End Time: {calculateEndTime()}</p>
                  <p>Duration: {formData.duration} minutes</p>
                </div>
              )}
            </div>

            {/* Additional Details */}
            <div className={styles.formSection}>
              <h3>Additional Details</h3>
              <div className={styles.formGroup}>
                <label htmlFor="location">Location</label>
                <select
                  id="location"
                  name="location"
                  value={formData.location}
                  onChange={handleInputChange}
                  className={styles.formControl}
                >
                  <option value="Studio">Studio</option>
                  <option value="Client Location">Client Location</option>
                  <option value="Event Venue">Event Venue</option>
                  <option value="Mobile Service">Mobile Service</option>
                </select>
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="notes">Notes</label>
                <textarea
                  id="notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleInputChange}
                  rows={4}
                  placeholder="Any special requirements or notes for this booking..."
                  className={styles.formControl}
                />
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className={styles.formActions}>
            <button
              type="button"
              onClick={() => router.push('/admin/bookings')}
              className={styles.cancelButton}
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className={styles.submitButton}
              disabled={loading}
            >
              {loading ? 'Creating...' : 'Create Booking'}
            </button>
          </div>
        </form>
      </div>
    </AdminLayout>
  );
}
