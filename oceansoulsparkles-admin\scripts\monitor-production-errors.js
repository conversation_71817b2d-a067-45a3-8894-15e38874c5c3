/**
 * Ocean Soul Sparkles Admin - Production Error Monitoring Script
 * Monitors and reports on the status of critical production error fixes
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Production Error Monitoring for Ocean Soul Sparkles Admin\n');

// Monitor 1: Service Worker Status
function monitorServiceWorkerStatus() {
  console.log('1. Monitoring Service Worker Status...');
  
  const swPath = path.join(__dirname, '..', 'public', 'sw.js');
  const manifestPath = path.join(__dirname, '..', 'public', 'manifest.json');
  
  const swExists = fs.existsSync(swPath);
  const manifestExists = fs.existsSync(manifestPath);
  
  if (swExists && manifestExists) {
    console.log('   ✅ Service worker and manifest files are present');
    console.log('   📊 Status: Service Worker 404 Error - RESOLVED');
    return true;
  } else {
    console.log('   ❌ Missing PWA files:');
    if (!swExists) console.log('      - Service worker (sw.js) missing');
    if (!manifestExists) console.log('      - PWA manifest (manifest.json) missing');
    console.log('   📊 Status: Service Worker 404 Error - NOT RESOLVED');
    return false;
  }
}

// Monitor 2: Customer Data Caching Fix
function monitorCustomerCachingFix() {
  console.log('\n2. Monitoring Customer Data Caching Fix...');
  
  const pwaManagerPath = path.join(__dirname, '..', 'components', 'admin', 'PWAManager.tsx');
  const cacheManagerPath = path.join(__dirname, '..', 'lib', 'pwa', 'cache-manager.ts');
  
  let fixesApplied = 0;
  const totalFixes = 2;
  
  if (fs.existsSync(pwaManagerPath)) {
    const pwaContent = fs.readFileSync(pwaManagerPath, 'utf8');
    if (pwaContent.includes('customersData.customers || []') && 
        pwaContent.includes('Array.isArray(customers)')) {
      console.log('   ✅ PWAManager data extraction fix applied');
      fixesApplied++;
    } else {
      console.log('   ❌ PWAManager data extraction fix missing');
    }
  }
  
  if (fs.existsSync(cacheManagerPath)) {
    const cacheContent = fs.readFileSync(cacheManagerPath, 'utf8');
    if (cacheContent.includes('Array.isArray(customers)') && 
        cacheContent.includes('customers parameter must be an array')) {
      console.log('   ✅ Cache manager array validation fix applied');
      fixesApplied++;
    } else {
      console.log('   ❌ Cache manager array validation fix missing');
    }
  }
  
  if (fixesApplied === totalFixes) {
    console.log('   📊 Status: Customer Data Caching TypeError - RESOLVED');
    return true;
  } else {
    console.log(`   📊 Status: Customer Data Caching TypeError - PARTIALLY RESOLVED (${fixesApplied}/${totalFixes})`);
    return false;
  }
}

// Monitor 3: Runtime Port Communication Fix
function monitorRuntimePortFix() {
  console.log('\n3. Monitoring Runtime Port Communication Fix...');
  
  const pwaManagerPath = path.join(__dirname, '..', 'components', 'admin', 'PWAManager.tsx');
  const swPath = path.join(__dirname, '..', 'public', 'sw.js');
  
  let fixesApplied = 0;
  const totalFixes = 2;
  
  if (fs.existsSync(pwaManagerPath)) {
    const pwaContent = fs.readFileSync(pwaManagerPath, 'utf8');
    if (pwaContent.includes('addEventListener(\'message\'') && 
        pwaContent.includes('catch (error)')) {
      console.log('   ✅ PWAManager message handling fix applied');
      fixesApplied++;
    } else {
      console.log('   ❌ PWAManager message handling fix missing');
    }
  }
  
  if (fs.existsSync(swPath)) {
    const swContent = fs.readFileSync(swPath, 'utf8');
    if (swContent.includes('addEventListener(\'message\'') && 
        swContent.includes('console.log(\'Service Worker: Message received\'')) {
      console.log('   ✅ Service worker message handling implemented');
      fixesApplied++;
    } else {
      console.log('   ❌ Service worker message handling missing');
    }
  }
  
  if (fixesApplied === totalFixes) {
    console.log('   📊 Status: Runtime Port Communication Error - RESOLVED');
    return true;
  } else {
    console.log(`   📊 Status: Runtime Port Communication Error - PARTIALLY RESOLVED (${fixesApplied}/${totalFixes})`);
    return false;
  }
}

// Monitor 4: Booking API 400 Error Fix
function monitorBookingAPIFix() {
  console.log('\n4. Monitoring Booking API 400 Error Fix...');
  
  const apiPath = path.join(__dirname, '..', 'pages', 'api', 'admin', 'bookings.ts');
  const formPath = path.join(__dirname, '..', 'pages', 'admin', 'bookings', 'new.js');
  
  let fixesApplied = 0;
  const totalFixes = 4;
  
  if (fs.existsSync(apiPath)) {
    const apiContent = fs.readFileSync(apiPath, 'utf8');
    
    // Check for enhanced validation
    if (apiContent.includes('Invalid date format') && 
        apiContent.includes('isNaN(startDate.getTime())')) {
      console.log('   ✅ API date validation fix applied');
      fixesApplied++;
    } else {
      console.log('   ❌ API date validation fix missing');
    }
    
    // Check for detailed error handling
    if (apiContent.includes('23503') && apiContent.includes('23514')) {
      console.log('   ✅ API database constraint handling applied');
      fixesApplied++;
    } else {
      console.log('   ❌ API database constraint handling missing');
    }
  }
  
  if (fs.existsSync(formPath)) {
    const formContent = fs.readFileSync(formPath, 'utf8');
    
    // Check for data type conversion
    if (formContent.includes('parseInt(formData.customer_id)') && 
        formContent.includes('parseFloat(selectedTier.price)')) {
      console.log('   ✅ Frontend data type conversion fix applied');
      fixesApplied++;
    } else {
      console.log('   ❌ Frontend data type conversion fix missing');
    }
    
    // Check for enhanced error messages
    if (formContent.includes('Invalid customer, service, or artist selected') && 
        formContent.includes('Network error: Failed to create booking')) {
      console.log('   ✅ Frontend error message enhancement applied');
      fixesApplied++;
    } else {
      console.log('   ❌ Frontend error message enhancement missing');
    }
  }
  
  if (fixesApplied === totalFixes) {
    console.log('   📊 Status: Booking API 400 Error - RESOLVED');
    return true;
  } else {
    console.log(`   📊 Status: Booking API 400 Error - PARTIALLY RESOLVED (${fixesApplied}/${totalFixes})`);
    return false;
  }
}

// Monitor 5: Overall System Health
function monitorOverallSystemHealth() {
  console.log('\n5. Monitoring Overall System Health...');
  
  const criticalFiles = [
    'public/sw.js',
    'public/manifest.json',
    'components/admin/PWAManager.tsx',
    'lib/pwa/cache-manager.ts',
    'pages/api/admin/bookings.ts',
    'pages/admin/bookings/new.js',
    'pages/_app.tsx'
  ];
  
  let healthyFiles = 0;
  
  criticalFiles.forEach(filePath => {
    const fullPath = path.join(__dirname, '..', filePath);
    if (fs.existsSync(fullPath)) {
      healthyFiles++;
    } else {
      console.log(`   ❌ Missing critical file: ${filePath}`);
    }
  });
  
  const healthPercentage = Math.round((healthyFiles / criticalFiles.length) * 100);
  
  if (healthPercentage === 100) {
    console.log('   ✅ All critical system files are present');
    console.log('   📊 System Health: EXCELLENT (100%)');
    return true;
  } else {
    console.log(`   ⚠️  System Health: ${healthPercentage}% (${healthyFiles}/${criticalFiles.length} files present)`);
    return false;
  }
}

// Monitor 6: Documentation Status
function monitorDocumentationStatus() {
  console.log('\n6. Monitoring Documentation Status...');
  
  const docFiles = [
    'docs/PWA-Production-Error-Resolution.md',
    'docs/Database-Backup-Recovery-Plan.md',
    'docs/UAT-Test-Scenarios.md',
    'docs/Training-Materials.md'
  ];
  
  let existingDocs = 0;
  
  docFiles.forEach(docPath => {
    const fullPath = path.join(__dirname, '..', docPath);
    if (fs.existsSync(fullPath)) {
      existingDocs++;
    }
  });
  
  const docPercentage = Math.round((existingDocs / docFiles.length) * 100);
  
  if (docPercentage >= 75) {
    console.log(`   ✅ Documentation coverage: ${docPercentage}% (${existingDocs}/${docFiles.length} docs)`);
    return true;
  } else {
    console.log(`   ⚠️  Documentation coverage: ${docPercentage}% (${existingDocs}/${docFiles.length} docs)`);
    return false;
  }
}

// Generate Production Readiness Report
function generateProductionReadinessReport(results) {
  console.log('\n' + '='.repeat(70));
  console.log('📋 PRODUCTION READINESS REPORT');
  console.log('='.repeat(70));
  
  const resolvedIssues = results.filter(r => r.resolved).length;
  const totalIssues = results.length;
  const readinessPercentage = Math.round((resolvedIssues / totalIssues) * 100);
  
  console.log(`\n🎯 Overall Production Readiness: ${readinessPercentage}%`);
  console.log(`📊 Resolved Issues: ${resolvedIssues}/${totalIssues}`);
  
  console.log('\n📋 Issue Status Summary:');
  results.forEach(({ name, resolved, status }) => {
    console.log(`   ${resolved ? '✅' : '❌'} ${name}: ${status}`);
  });
  
  if (readinessPercentage >= 90) {
    console.log('\n🎉 PRODUCTION READY!');
    console.log('✅ All critical errors have been resolved');
    console.log('🚀 System is ready for stable production deployment');
    
    console.log('\n📋 Deployment Checklist:');
    console.log('   ✅ Service worker files deployed');
    console.log('   ✅ PWA functionality operational');
    console.log('   ✅ Customer data caching fixed');
    console.log('   ✅ Runtime communication stable');
    console.log('   ✅ Booking API errors resolved');
    console.log('   ✅ Error handling enhanced');
    console.log('   ✅ Documentation updated');
    
  } else if (readinessPercentage >= 75) {
    console.log('\n⚠️  MOSTLY READY - Minor Issues Remaining');
    console.log('🔧 Some non-critical issues need attention');
    console.log('✅ Safe to deploy with monitoring');
    
  } else {
    console.log('\n❌ NOT READY FOR PRODUCTION');
    console.log('🚨 Critical issues need immediate attention');
    console.log('⏳ Complete remaining fixes before deployment');
  }
  
  console.log('\n📞 Support Information:');
  console.log('   📧 Technical Support: Development Team');
  console.log('   📱 Emergency Contact: System Administrator');
  console.log('   📚 Documentation: /docs/ directory');
  
  console.log('\n' + '='.repeat(70));
}

// Run all monitoring checks
async function runProductionMonitoring() {
  console.log('🚀 Starting Production Error Monitoring...\n');
  
  const monitors = [
    { name: 'Service Worker 404 Error', monitor: monitorServiceWorkerStatus },
    { name: 'Customer Data Caching TypeError', monitor: monitorCustomerCachingFix },
    { name: 'Runtime Port Communication Error', monitor: monitorRuntimePortFix },
    { name: 'Booking API 400 Error', monitor: monitorBookingAPIFix },
    { name: 'Overall System Health', monitor: monitorOverallSystemHealth },
    { name: 'Documentation Status', monitor: monitorDocumentationStatus }
  ];
  
  const results = monitors.map(({ name, monitor }) => {
    const resolved = monitor();
    return {
      name,
      resolved,
      status: resolved ? 'RESOLVED' : 'NEEDS ATTENTION'
    };
  });
  
  generateProductionReadinessReport(results);
  
  return results;
}

// Execute the monitoring
runProductionMonitoring().catch(error => {
  console.error('❌ Monitoring execution failed:', error);
  process.exit(1);
});
