/* Customer Analytics Styles */
.analyticsContainer {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.analyticsHeader {
  text-align: center;
  margin-bottom: 2rem;
}

.analyticsHeader h2 {
  margin: 0 0 0.5rem 0;
  color: #1e293b;
  font-size: 1.75rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.analyticsHeader p {
  margin: 0;
  color: #64748b;
  font-size: 1rem;
}

.metricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.metricCard {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 16px;
  text-align: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  transition: transform 0.3s ease;
}

.metricCard:hover {
  transform: translateY(-4px);
}

.metricCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
  pointer-events: none;
}

.metricValue {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.metricLabel {
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  opacity: 0.9;
}

.metricIcon {
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 2rem;
  opacity: 0.3;
}

.chartsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.chartCard {
  background: white;
  padding: 2rem;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.chartCard h3 {
  margin: 0 0 1.5rem 0;
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #667eea;
}

.barChart {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  height: 200px;
  gap: 0.5rem;
  padding: 1rem 0;
}

.barContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
}

.bar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  width: 100%;
  max-width: 40px;
  border-radius: 4px 4px 0 0;
  transition: all 0.3s ease;
  position: relative;
  margin-bottom: auto;
}

.bar:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  transform: scaleY(1.05);
}

.barLabel {
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: #64748b;
  font-weight: 500;
  text-align: center;
}

.barValue {
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: #1e293b;
  font-weight: 600;
}

.topCustomersList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.topCustomerItem {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.topCustomerItem:hover {
  background: #f1f5f9;
  transform: translateX(4px);
}

.customerRank {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.customerInfo {
  flex: 1;
}

.customerName {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.25rem;
}

.customerEmail {
  color: #64748b;
  font-size: 0.875rem;
}

.customerBookings {
  text-align: right;
}

.bookingCount {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
}

.bookingLabel {
  color: #64748b;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.emptyState {
  text-align: center;
  padding: 2rem;
  color: #64748b;
}

.insightsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.insightCard {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  text-align: center;
}

.insightCard h4 {
  margin: 0 0 1rem 0;
  color: #1e293b;
  font-size: 1rem;
  font-weight: 600;
}

.insightContent {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.engagementStat, .trendStat, .retentionStat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.percentage, .trendValue, .retentionValue {
  font-size: 2rem;
  font-weight: 700;
  color: #667eea;
}

.engagementLabel, .trendLabel, .retentionLabel {
  color: #64748b;
  font-size: 0.875rem;
  text-align: center;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .chartsGrid {
    grid-template-columns: 1fr;
  }
  
  .metricsGrid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
}

@media (max-width: 768px) {
  .analyticsContainer {
    padding: 1.5rem;
  }
  
  .metricsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .metricCard {
    padding: 1.5rem;
  }
  
  .metricValue {
    font-size: 2rem;
  }
  
  .chartCard {
    padding: 1.5rem;
  }
  
  .barChart {
    height: 150px;
  }
  
  .insightsGrid {
    grid-template-columns: 1fr;
  }
  
  .topCustomerItem {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }
  
  .customerBookings {
    text-align: center;
  }
}
