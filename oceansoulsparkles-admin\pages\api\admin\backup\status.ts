/**
 * Ocean Soul Sparkles Admin Dashboard - Backup Status API
 * Provides backup monitoring and status information
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { withMiddleware, authenticatedMiddleware } from '../../../../lib/errors/api-error-middleware';
import { createClient } from '@supabase/supabase-js';
import { ApiResponse } from '../../../../types/api';

interface BackupStatus {
  pitr_enabled: boolean;
  last_backup: {
    date: string;
    status: string;
    size_mb: number;
    duration_seconds: number;
  } | null;
  backup_health: {
    success_rate_7d: number;
    total_backups_7d: number;
    failed_backups_7d: number;
    avg_duration_7d: number;
  };
  storage_info: {
    total_size_mb: number;
    retention_days: number;
    estimated_cost_monthly: number;
  };
  data_integrity: {
    last_verified: string;
    tables_verified: number;
    total_rows: number;
    status: 'healthy' | 'warning' | 'error';
  };
  alerts: Array<{
    type: 'warning' | 'error' | 'info';
    message: string;
    timestamp: string;
  }>;
}

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
const supabase = createClient(supabaseUrl, supabaseKey);

async function handler(req: NextApiRequest, res: NextApiResponse<ApiResponse<BackupStatus>>) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      error: {
        code: 'METHOD_NOT_ALLOWED',
        message: `Method ${req.method} not allowed`
      }
    });
  }

  try {
    // Get backup configuration status
    const backupConfig = await getBackupConfiguration();
    
    // Get recent backup history
    const backupHistory = await getBackupHistory();
    
    // Get data integrity status
    const integrityStatus = await getDataIntegrityStatus();
    
    // Calculate backup health metrics
    const healthMetrics = calculateBackupHealth(backupHistory);
    
    // Generate alerts based on status
    const alerts = generateBackupAlerts(backupConfig, backupHistory, integrityStatus);

    const status: BackupStatus = {
      pitr_enabled: backupConfig.pitr_enabled,
      last_backup: backupHistory.length > 0 ? {
        date: backupHistory[0].backup_date,
        status: backupHistory[0].status,
        size_mb: backupHistory[0].size_mb || 0,
        duration_seconds: backupHistory[0].duration_seconds || 0
      } : null,
      backup_health: healthMetrics,
      storage_info: {
        total_size_mb: calculateTotalStorageSize(backupHistory),
        retention_days: 90, // Default retention
        estimated_cost_monthly: 50 // Estimated cost
      },
      data_integrity: integrityStatus,
      alerts
    };

    res.status(200).json({
      success: true,
      data: status,
      meta: {
        requestId: (req as any).context?.requestId || 'unknown',
        timestamp: new Date().toISOString(),
        version: '1.0'
      }
    });

  } catch (error) {
    console.error('Error fetching backup status:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to fetch backup status'
      }
    });
  }
}

async function getBackupConfiguration() {
  try {
    // In a real implementation, this would check Supabase backup settings
    // For now, we'll return the current known status
    return {
      pitr_enabled: false, // Currently disabled
      daily_backups_enabled: false,
      retention_days: 0
    };
  } catch (error) {
    console.error('Error getting backup configuration:', error);
    return {
      pitr_enabled: false,
      daily_backups_enabled: false,
      retention_days: 0
    };
  }
}

async function getBackupHistory() {
  try {
    const { data, error } = await supabase
      .from('backup_monitoring')
      .select('*')
      .order('backup_date', { ascending: false })
      .limit(30);

    if (error) {
      console.error('Error fetching backup history:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching backup history:', error);
    return [];
  }
}

async function getDataIntegrityStatus() {
  try {
    const { data, error } = await supabase
      .rpc('verify_backup_integrity');

    if (error) {
      console.error('Error verifying data integrity:', error);
      return {
        last_verified: new Date().toISOString(),
        tables_verified: 0,
        total_rows: 0,
        status: 'error' as const
      };
    }

    const totalRows = data?.reduce((sum: number, table: any) => sum + (table.row_count || 0), 0) || 0;
    const tablesVerified = data?.length || 0;

    return {
      last_verified: new Date().toISOString(),
      tables_verified: tablesVerified,
      total_rows: totalRows,
      status: totalRows > 0 ? 'healthy' as const : 'warning' as const
    };
  } catch (error) {
    console.error('Error checking data integrity:', error);
    return {
      last_verified: new Date().toISOString(),
      tables_verified: 0,
      total_rows: 0,
      status: 'error' as const
    };
  }
}

function calculateBackupHealth(backupHistory: any[]) {
  const last7Days = backupHistory.filter(backup => {
    const backupDate = new Date(backup.backup_date);
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    return backupDate >= sevenDaysAgo;
  });

  const totalBackups = last7Days.length;
  const failedBackups = last7Days.filter(backup => backup.status === 'failed').length;
  const successfulBackups = totalBackups - failedBackups;
  const successRate = totalBackups > 0 ? (successfulBackups / totalBackups) * 100 : 0;

  const avgDuration = last7Days.length > 0 
    ? last7Days.reduce((sum, backup) => sum + (backup.duration_seconds || 0), 0) / last7Days.length
    : 0;

  return {
    success_rate_7d: Math.round(successRate * 100) / 100,
    total_backups_7d: totalBackups,
    failed_backups_7d: failedBackups,
    avg_duration_7d: Math.round(avgDuration)
  };
}

function calculateTotalStorageSize(backupHistory: any[]) {
  return backupHistory.reduce((total, backup) => total + (backup.size_mb || 0), 0);
}

function generateBackupAlerts(config: any, history: any[], integrity: any) {
  const alerts: Array<{
    type: 'warning' | 'error' | 'info';
    message: string;
    timestamp: string;
  }> = [];

  const now = new Date().toISOString();

  // Critical: No PITR enabled
  if (!config.pitr_enabled) {
    alerts.push({
      type: 'error',
      message: 'CRITICAL: Point-in-Time Recovery (PITR) is not enabled. Data loss risk is high.',
      timestamp: now
    });
  }

  // Critical: No recent backups
  if (history.length === 0) {
    alerts.push({
      type: 'error',
      message: 'CRITICAL: No backup history found. Automated backups may not be configured.',
      timestamp: now
    });
  } else {
    // Check for recent backup failures
    const recentFailures = history.slice(0, 3).filter(backup => backup.status === 'failed');
    if (recentFailures.length > 0) {
      alerts.push({
        type: 'error',
        message: `Backup failures detected: ${recentFailures.length} of last 3 backups failed.`,
        timestamp: now
      });
    }

    // Check for old backups
    const lastBackup = new Date(history[0].backup_date);
    const daysSinceLastBackup = (Date.now() - lastBackup.getTime()) / (1000 * 60 * 60 * 24);
    
    if (daysSinceLastBackup > 2) {
      alerts.push({
        type: 'warning',
        message: `Last backup was ${Math.round(daysSinceLastBackup)} days ago. Daily backups may not be running.`,
        timestamp: now
      });
    }
  }

  // Data integrity warnings
  if (integrity.status === 'error') {
    alerts.push({
      type: 'error',
      message: 'Data integrity check failed. Database may have issues.',
      timestamp: now
    });
  } else if (integrity.total_rows === 0) {
    alerts.push({
      type: 'warning',
      message: 'No data found in database tables. This may indicate a problem.',
      timestamp: now
    });
  }

  // Info: Backup recommendations
  if (config.pitr_enabled && history.length > 0) {
    alerts.push({
      type: 'info',
      message: 'Backup system is operational. Consider testing recovery procedures monthly.',
      timestamp: now
    });
  }

  return alerts;
}

export default withMiddleware(handler, authenticatedMiddleware);
