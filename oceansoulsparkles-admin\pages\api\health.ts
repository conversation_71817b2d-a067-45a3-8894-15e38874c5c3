/**
 * Ocean Soul Sparkles Admin Dashboard - Health Check API
 * Provides application health status and uptime information
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { createHealthCheckResponse } from '../../lib/monitoring/uptime-monitor';
import { createClient } from '@supabase/supabase-js';

interface HealthCheckResponse {
  status: 'healthy' | 'degraded' | 'down';
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
  services: {
    database: {
      status: 'healthy' | 'degraded' | 'down';
      responseTime?: number;
      error?: string;
    };
    api: {
      status: 'healthy' | 'degraded' | 'down';
      responseTime: number;
    };
    memory: {
      used: number;
      total: number;
      percentage: number;
      status: 'healthy' | 'warning' | 'critical';
    };
  };
  endpoints?: { [key: string]: any };
}

export default async function handler(req: NextApiRequest, res: NextApiResponse<HealthCheckResponse>) {
  const startTime = Date.now();

  try {
    // Get basic health check data
    const healthData = createHealthCheckResponse();
    
    // Test database connection
    const databaseHealth = await checkDatabaseHealth();
    
    // Calculate API response time
    const apiResponseTime = Date.now() - startTime;
    
    // Determine memory status
    const memoryStatus = healthData.memory.percentage > 90 ? 'critical' :
                        healthData.memory.percentage > 80 ? 'warning' : 'healthy';
    
    // Determine overall status
    let overallStatus: 'healthy' | 'degraded' | 'down' = 'healthy';
    
    if (databaseHealth.status === 'down' || memoryStatus === 'critical') {
      overallStatus = 'down';
    } else if (databaseHealth.status === 'degraded' || memoryStatus === 'warning' || healthData.uptime < 99) {
      overallStatus = 'degraded';
    }

    const response: HealthCheckResponse = {
      status: overallStatus,
      timestamp: healthData.timestamp,
      uptime: healthData.uptime,
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {
        database: databaseHealth,
        api: {
          status: 'healthy',
          responseTime: apiResponseTime
        },
        memory: {
          ...healthData.memory,
          status: memoryStatus
        }
      }
    };

    // Include detailed endpoint data if requested
    if (req.query.detailed === 'true') {
      response.endpoints = healthData.endpoints;
    }

    // Set appropriate HTTP status code
    const httpStatus = overallStatus === 'healthy' ? 200 :
                      overallStatus === 'degraded' ? 200 : 503;

    // Set cache headers
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    res.status(httpStatus).json(response);

  } catch (error) {
    console.error('Health check error:', error);
    
    res.status(503).json({
      status: 'down',
      timestamp: new Date().toISOString(),
      uptime: 0,
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {
        database: {
          status: 'down',
          error: 'Health check failed'
        },
        api: {
          status: 'down',
          responseTime: Date.now() - startTime
        },
        memory: {
          used: 0,
          total: 0,
          percentage: 0,
          status: 'critical'
        }
      }
    });
  }
}

/**
 * Check database health by performing a simple query
 */
async function checkDatabaseHealth(): Promise<{
  status: 'healthy' | 'degraded' | 'down';
  responseTime?: number;
  error?: string;
}> {
  const startTime = Date.now();

  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseKey) {
      return {
        status: 'down',
        error: 'Database configuration missing'
      };
    }

    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Perform a simple query to test database connectivity
    const { data, error } = await supabase
      .from('admin_users')
      .select('count')
      .limit(1);

    const responseTime = Date.now() - startTime;

    if (error) {
      return {
        status: 'down',
        responseTime,
        error: error.message
      };
    }

    // Determine status based on response time
    const status = responseTime > 1000 ? 'degraded' : 'healthy';

    return {
      status,
      responseTime
    };

  } catch (error) {
    const responseTime = Date.now() - startTime;
    return {
      status: 'down',
      responseTime,
      error: error instanceof Error ? error.message : 'Unknown database error'
    };
  }
}
