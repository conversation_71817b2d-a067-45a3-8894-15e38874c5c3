/**
 * Ocean Soul Sparkles Admin Dashboard - Performance Monitoring API
 * API endpoint for retrieving performance metrics and alerts
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { withMiddleware, authenticatedMiddleware } from '../../../../lib/errors/api-error-middleware';
import { 
  getPerformanceMetrics, 
  getActivePerformanceAlerts, 
  resolvePerformanceAlert,
  recordSystemPerformance 
} from '../../../../lib/monitoring/performance-monitor';
import { ApiResponse } from '../../../../types/api';

interface PerformanceData {
  metrics: any[];
  alerts: any[];
  summary: {
    avgResponseTime: number;
    totalRequests: number;
    errorRate: number;
    slowRequests: number;
    activeAlerts: number;
  };
}

async function handler(req: NextApiRequest, res: NextApiResponse<ApiResponse<PerformanceData>>) {
  const { method, query } = req;

  switch (method) {
    case 'GET':
      await handleGetPerformance(req, res);
      break;
    case 'POST':
      await handlePerformanceAction(req, res);
      break;
    default:
      res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: `Method ${method} not allowed`
        }
      });
  }
}

async function handleGetPerformance(req: NextApiRequest, res: NextApiResponse<ApiResponse<PerformanceData>>) {
  try {
    const { 
      startDate = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // Last 24 hours
      endDate = new Date().toISOString(),
      metricType,
      endpoint 
    } = req.query;

    // Get performance metrics
    const metrics = await getPerformanceMetrics(
      startDate as string,
      endDate as string,
      metricType as string,
      endpoint as string
    );

    // Get active alerts
    const alerts = await getActivePerformanceAlerts();

    // Calculate summary statistics
    const apiMetrics = metrics.filter(m => m.metric_type === 'api_response');
    const totalRequests = apiMetrics.length;
    const errorRequests = apiMetrics.filter(m => m.status_code && m.status_code >= 400).length;
    const slowRequests = apiMetrics.filter(m => m.duration > 500).length;
    
    const avgResponseTime = totalRequests > 0 
      ? Math.round(apiMetrics.reduce((sum, m) => sum + m.duration, 0) / totalRequests)
      : 0;
    
    const errorRate = totalRequests > 0 
      ? Math.round((errorRequests / totalRequests) * 100 * 100) / 100
      : 0;

    const summary = {
      avgResponseTime,
      totalRequests,
      errorRate,
      slowRequests,
      activeAlerts: alerts.length
    };

    res.status(200).json({
      success: true,
      data: {
        metrics,
        alerts,
        summary
      },
      meta: {
        requestId: (req as any).context?.requestId || 'unknown',
        timestamp: new Date().toISOString(),
        version: '1.0'
      }
    });

  } catch (error) {
    console.error('Error fetching performance data:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to fetch performance data'
      }
    });
  }
}

async function handlePerformanceAction(req: NextApiRequest, res: NextApiResponse<ApiResponse<any>>) {
  try {
    const { action, alertId } = req.body;

    switch (action) {
      case 'resolve_alert':
        if (!alertId) {
          return res.status(400).json({
            success: false,
            error: {
              code: 'INVALID_INPUT',
              message: 'Alert ID is required'
            }
          });
        }

        const resolved = await resolvePerformanceAlert(alertId);
        if (!resolved) {
          return res.status(500).json({
            success: false,
            error: {
              code: 'INTERNAL_ERROR',
              message: 'Failed to resolve alert'
            }
          });
        }

        res.status(200).json({
          success: true,
          data: { resolved: true }
        });
        break;

      case 'record_system_metrics':
        await recordSystemPerformance();
        res.status(200).json({
          success: true,
          data: { recorded: true }
        });
        break;

      default:
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_ACTION',
            message: `Unknown action: ${action}`
          }
        });
    }

  } catch (error) {
    console.error('Error handling performance action:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to handle performance action'
      }
    });
  }
}

export default withMiddleware(handler, authenticatedMiddleware);
