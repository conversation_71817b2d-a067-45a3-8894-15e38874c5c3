/* Settings Management Page Styles */

.settingsContainer {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.title {
  font-size: 1.8rem;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.headerActions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.resetBtn, .saveBtn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
}

.resetBtn {
  background: #f8fafc;
  color: #475569;
  border: 1px solid #e2e8f0;
}

.resetBtn:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.saveBtn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.saveBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.saveBtn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.settingsContent {
  display: flex;
  min-height: calc(100vh - 80px);
}

.tabNavigation {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  width: 250px;
  padding: 2rem 0;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
}

.tabButton {
  background: none;
  border: none;
  padding: 1rem 2rem;
  text-align: left;
  font-size: 1rem;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.tabButton:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.tabButton.active {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  border-left-color: #667eea;
  font-weight: 600;
}

.tabContent {
  flex: 1;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.settingsSection h2 {
  color: #1e293b;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 2rem 0;
}

.settingsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.settingItem {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.settingItem label {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.settingItem input,
.settingItem select,
.settingItem textarea {
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  transition: all 0.3s ease;
}

.settingItem input:focus,
.settingItem select:focus,
.settingItem textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.settingItem input[type="checkbox"] {
  width: auto;
  margin-right: 0.5rem;
}

.settingItem textarea {
  resize: vertical;
  min-height: 80px;
}

.comingSoon {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  color: #64748b;
  font-size: 1.1rem;
  text-align: center;
  background: rgba(248, 250, 252, 0.5);
  border-radius: 12px;
  border: 2px dashed #e2e8f0;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  color: #64748b;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.accessDenied {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  color: #64748b;
}

.accessDenied h2 {
  color: #1e293b;
  margin-bottom: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .headerActions {
    justify-content: center;
  }

  .settingsContent {
    flex-direction: column;
  }

  .tabNavigation {
    width: 100%;
    flex-direction: row;
    overflow-x: auto;
    padding: 1rem 0;
  }

  .tabButton {
    white-space: nowrap;
    border-left: none;
    border-bottom: 3px solid transparent;
    padding: 1rem 1.5rem;
  }

  .tabButton.active {
    border-left: none;
    border-bottom-color: #667eea;
  }

  .tabContent {
    padding: 1rem;
  }

  .settingsGrid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .tabNavigation {
    padding: 0.5rem 0;
  }

  .tabButton {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }

  .settingsSection h2 {
    font-size: 1.25rem;
  }
}
