/**
 * Ocean Soul Sparkles Admin Dashboard - Performance Monitoring System
 * Real-time performance monitoring for API endpoints and database queries
 */

import { createClient } from '@supabase/supabase-js';
import { auditLog } from '../security/audit-logging';

// Performance monitoring configuration
const PERFORMANCE_THRESHOLDS = {
  API_RESPONSE_WARNING: 500, // ms
  API_RESPONSE_CRITICAL: 1000, // ms
  DATABASE_QUERY_WARNING: 200, // ms
  DATABASE_QUERY_CRITICAL: 500, // ms
  MEMORY_WARNING: 80, // percentage
  MEMORY_CRITICAL: 90, // percentage
};

// Supabase client for monitoring data
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
const supabase = createClient(supabaseUrl, supabaseKey);

// Performance metrics interface
export interface PerformanceMetric {
  id?: string;
  metric_type: 'api_response' | 'database_query' | 'page_load' | 'memory_usage' | 'uptime_check';
  endpoint?: string;
  method?: string;
  duration: number;
  status_code?: number;
  error_message?: string;
  user_id?: string;
  ip_address?: string;
  user_agent?: string;
  memory_usage?: number;
  cpu_usage?: number;
  metadata?: Record<string, any>;
  created_at?: string;
}

// Performance alert interface
export interface PerformanceAlert {
  id?: string;
  alert_type: 'warning' | 'critical';
  metric_type: string;
  endpoint?: string;
  threshold_value: number;
  actual_value: number;
  message: string;
  resolved: boolean;
  created_at?: string;
  resolved_at?: string;
}

/**
 * Record API performance metric
 */
export async function recordAPIPerformance(
  endpoint: string,
  method: string,
  duration: number,
  statusCode: number,
  userId?: string,
  ip?: string,
  userAgent?: string,
  error?: string
): Promise<void> {
  try {
    const metric: PerformanceMetric = {
      metric_type: 'api_response',
      endpoint,
      method,
      duration,
      status_code: statusCode,
      error_message: error,
      user_id: userId,
      ip_address: ip,
      user_agent: userAgent,
      created_at: new Date().toISOString()
    };

    // Store metric in database
    await storePerformanceMetric(metric);

    // Check for performance alerts
    await checkPerformanceThresholds(metric);

    // Log to console for immediate visibility
    const status = statusCode >= 400 ? 'ERROR' : 'SUCCESS';
    const performanceLevel = duration > PERFORMANCE_THRESHOLDS.API_RESPONSE_CRITICAL ? 'CRITICAL' :
                           duration > PERFORMANCE_THRESHOLDS.API_RESPONSE_WARNING ? 'WARNING' : 'OK';
    
    console.log(`[PERF] ${method} ${endpoint} - ${status} (${duration}ms) [${performanceLevel}]`);

  } catch (error) {
    console.error('Failed to record API performance:', error);
  }
}

/**
 * Record database query performance
 */
export async function recordDatabasePerformance(
  query: string,
  duration: number,
  table?: string,
  operation?: string,
  error?: string
): Promise<void> {
  try {
    const metric: PerformanceMetric = {
      metric_type: 'database_query',
      duration,
      error_message: error,
      metadata: {
        query: query.substring(0, 200), // Truncate long queries
        table,
        operation
      },
      created_at: new Date().toISOString()
    };

    // Store metric in database
    await storePerformanceMetric(metric);

    // Check for performance alerts
    await checkDatabasePerformanceThresholds(metric);

    // Log to console
    const status = error ? 'ERROR' : 'SUCCESS';
    const performanceLevel = duration > PERFORMANCE_THRESHOLDS.DATABASE_QUERY_CRITICAL ? 'CRITICAL' :
                           duration > PERFORMANCE_THRESHOLDS.DATABASE_QUERY_WARNING ? 'WARNING' : 'OK';
    
    console.log(`[DB_PERF] ${operation || 'QUERY'} ${table || ''} - ${status} (${duration}ms) [${performanceLevel}]`);

  } catch (error) {
    console.error('Failed to record database performance:', error);
  }
}

/**
 * Record system performance metrics
 */
export async function recordSystemPerformance(): Promise<void> {
  try {
    const memoryUsage = process.memoryUsage();
    const memoryUsedMB = Math.round(memoryUsage.heapUsed / 1024 / 1024);
    const memoryTotalMB = Math.round(memoryUsage.heapTotal / 1024 / 1024);
    const memoryPercentage = Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100);

    const metric: PerformanceMetric = {
      metric_type: 'memory_usage',
      duration: 0, // Not applicable for memory metrics
      memory_usage: memoryPercentage,
      metadata: {
        heapUsed: memoryUsedMB,
        heapTotal: memoryTotalMB,
        external: Math.round(memoryUsage.external / 1024 / 1024),
        rss: Math.round(memoryUsage.rss / 1024 / 1024)
      },
      created_at: new Date().toISOString()
    };

    // Store metric in database
    await storePerformanceMetric(metric);

    // Check for memory alerts
    if (memoryPercentage > PERFORMANCE_THRESHOLDS.MEMORY_CRITICAL) {
      await createPerformanceAlert({
        alert_type: 'critical',
        metric_type: 'memory_usage',
        threshold_value: PERFORMANCE_THRESHOLDS.MEMORY_CRITICAL,
        actual_value: memoryPercentage,
        message: `Critical memory usage: ${memoryPercentage}% (${memoryUsedMB}MB used)`,
        resolved: false
      });
    } else if (memoryPercentage > PERFORMANCE_THRESHOLDS.MEMORY_WARNING) {
      await createPerformanceAlert({
        alert_type: 'warning',
        metric_type: 'memory_usage',
        threshold_value: PERFORMANCE_THRESHOLDS.MEMORY_WARNING,
        actual_value: memoryPercentage,
        message: `High memory usage: ${memoryPercentage}% (${memoryUsedMB}MB used)`,
        resolved: false
      });
    }

  } catch (error) {
    console.error('Failed to record system performance:', error);
  }
}

/**
 * Store performance metric in database
 */
async function storePerformanceMetric(metric: PerformanceMetric): Promise<void> {
  try {
    const { error } = await supabase
      .from('performance_metrics')
      .insert(metric);

    if (error) {
      console.error('Failed to store performance metric:', error);
    }
  } catch (error) {
    console.error('Database error storing performance metric:', error);
  }
}

/**
 * Check API performance thresholds and create alerts
 */
async function checkPerformanceThresholds(metric: PerformanceMetric): Promise<void> {
  if (metric.metric_type !== 'api_response' || !metric.endpoint) return;

  if (metric.duration > PERFORMANCE_THRESHOLDS.API_RESPONSE_CRITICAL) {
    await createPerformanceAlert({
      alert_type: 'critical',
      metric_type: 'api_response',
      endpoint: metric.endpoint,
      threshold_value: PERFORMANCE_THRESHOLDS.API_RESPONSE_CRITICAL,
      actual_value: metric.duration,
      message: `Critical API response time: ${metric.method} ${metric.endpoint} took ${metric.duration}ms`,
      resolved: false
    });

    // Also log to audit system
    await auditLog({
      action: 'PERFORMANCE_CRITICAL',
      path: metric.endpoint,
      severity: 'critical',
      metadata: {
        duration: metric.duration,
        threshold: PERFORMANCE_THRESHOLDS.API_RESPONSE_CRITICAL,
        method: metric.method
      }
    });

  } else if (metric.duration > PERFORMANCE_THRESHOLDS.API_RESPONSE_WARNING) {
    await createPerformanceAlert({
      alert_type: 'warning',
      metric_type: 'api_response',
      endpoint: metric.endpoint,
      threshold_value: PERFORMANCE_THRESHOLDS.API_RESPONSE_WARNING,
      actual_value: metric.duration,
      message: `Slow API response time: ${metric.method} ${metric.endpoint} took ${metric.duration}ms`,
      resolved: false
    });
  }
}

/**
 * Check database performance thresholds
 */
async function checkDatabasePerformanceThresholds(metric: PerformanceMetric): Promise<void> {
  if (metric.metric_type !== 'database_query') return;

  if (metric.duration > PERFORMANCE_THRESHOLDS.DATABASE_QUERY_CRITICAL) {
    await createPerformanceAlert({
      alert_type: 'critical',
      metric_type: 'database_query',
      threshold_value: PERFORMANCE_THRESHOLDS.DATABASE_QUERY_CRITICAL,
      actual_value: metric.duration,
      message: `Critical database query time: ${metric.duration}ms`,
      resolved: false
    });
  } else if (metric.duration > PERFORMANCE_THRESHOLDS.DATABASE_QUERY_WARNING) {
    await createPerformanceAlert({
      alert_type: 'warning',
      metric_type: 'database_query',
      threshold_value: PERFORMANCE_THRESHOLDS.DATABASE_QUERY_WARNING,
      actual_value: metric.duration,
      message: `Slow database query: ${metric.duration}ms`,
      resolved: false
    });
  }
}

/**
 * Create performance alert
 */
async function createPerformanceAlert(alert: PerformanceAlert): Promise<void> {
  try {
    const { error } = await supabase
      .from('performance_alerts')
      .insert({
        ...alert,
        created_at: new Date().toISOString()
      });

    if (error) {
      console.error('Failed to create performance alert:', error);
    }

    // Log alert to console
    const level = alert.alert_type === 'critical' ? 'error' : 'warn';
    console[level](`[PERF_ALERT] ${alert.alert_type.toUpperCase()}: ${alert.message}`);

  } catch (error) {
    console.error('Failed to create performance alert:', error);
  }
}

/**
 * Get performance metrics for dashboard
 */
export async function getPerformanceMetrics(
  startDate: string,
  endDate: string,
  metricType?: string,
  endpoint?: string
): Promise<PerformanceMetric[]> {
  try {
    let query = supabase
      .from('performance_metrics')
      .select('*')
      .gte('created_at', startDate)
      .lte('created_at', endDate)
      .order('created_at', { ascending: false });

    if (metricType) {
      query = query.eq('metric_type', metricType);
    }

    if (endpoint) {
      query = query.eq('endpoint', endpoint);
    }

    const { data, error } = await query.limit(1000);

    if (error) {
      console.error('Failed to fetch performance metrics:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching performance metrics:', error);
    return [];
  }
}

/**
 * Get active performance alerts
 */
export async function getActivePerformanceAlerts(): Promise<PerformanceAlert[]> {
  try {
    const { data, error } = await supabase
      .from('performance_alerts')
      .select('*')
      .eq('resolved', false)
      .order('created_at', { ascending: false })
      .limit(50);

    if (error) {
      console.error('Failed to fetch performance alerts:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching performance alerts:', error);
    return [];
  }
}

/**
 * Resolve performance alert
 */
export async function resolvePerformanceAlert(alertId: string): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('performance_alerts')
      .update({
        resolved: true,
        resolved_at: new Date().toISOString()
      })
      .eq('id', alertId);

    if (error) {
      console.error('Failed to resolve performance alert:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error resolving performance alert:', error);
    return false;
  }
}

/**
 * Start system performance monitoring (call this on app startup)
 */
export function startPerformanceMonitoring(): void {
  // Record system metrics every 5 minutes
  setInterval(recordSystemPerformance, 5 * 60 * 1000);
  
  console.log('[PERF_MONITOR] Performance monitoring started');
}

/**
 * Performance monitoring middleware for API routes
 */
export function performanceMiddleware(endpoint: string) {
  return (req: any, res: any, next: any) => {
    const startTime = Date.now();
    const originalJson = res.json;

    res.json = function(data: any) {
      const duration = Date.now() - startTime;
      const statusCode = res.statusCode;

      // Record performance metric
      recordAPIPerformance(
        endpoint,
        req.method || 'GET',
        duration,
        statusCode,
        req.context?.user?.id,
        req.context?.ip,
        req.context?.userAgent,
        statusCode >= 400 ? data?.error?.message : undefined
      );

      return originalJson.call(this, data);
    };

    next();
  };
}
