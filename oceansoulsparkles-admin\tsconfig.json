{"compilerOptions": {"target": "es2017", "lib": ["dom", "dom.iterable", "es2017", "es2018.promise"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "downlevelIteration": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./*"], "@/admin/*": ["./components/admin/*"], "@/lib/*": ["./lib/*"], "@/hooks/*": ["./hooks/*"], "@/utils/*": ["./utils/*"], "@/types/*": ["./types/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}