import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { useAuth } from '@/hooks/useAuth';
import AdminLayout from '@/components/admin/AdminLayout';
import styles from '@/styles/admin/BookingDetails.module.css';

export default function BookingDetails() {
  const router = useRouter();
  const { id } = router.query;
  const { user, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);
  const [booking, setBooking] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (id && !authLoading && user) {
      loadBooking();
    }
  }, [id, authLoading, user]);

  const loadBooking = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('admin-token');
      const response = await fetch(`/api/admin/bookings/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch booking details');
      }

      const data = await response.json();
      setBooking(data.booking);
    } catch (error) {
      console.error('Error loading booking:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const updateBookingStatus = async (newStatus) => {
    try {
      const token = localStorage.getItem('admin-token');
      const response = await fetch(`/api/admin/bookings/${id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus })
      });

      if (!response.ok) {
        throw new Error('Failed to update booking status');
      }

      const data = await response.json();
      setBooking(data.booking);
    } catch (error) {
      console.error('Error updating booking status:', error);
      alert('Failed to update booking status: ' + error.message);
    }
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this booking? This action cannot be undone.')) {
      return;
    }

    try {
      const token = localStorage.getItem('admin-token');
      const response = await fetch(`/api/admin/bookings/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to delete booking');
      }

      router.push('/admin/bookings');
    } catch (error) {
      console.error('Error deleting booking:', error);
      alert('Failed to delete booking: ' + error.message);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD'
    }).format(amount || 0);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-AU');
  };

  const formatDateTime = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString('en-AU');
  };

  const formatDuration = (minutes) => {
    if (!minutes) return 'N/A';
    if (minutes >= 60) {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
    }
    return `${minutes}m`;
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'confirmed': return '#28a745';
      case 'pending': return '#ffc107';
      case 'cancelled': return '#dc3545';
      case 'completed': return '#17a2b8';
      case 'no_show': return '#6c757d';
      default: return '#6c757d';
    }
  };

  if (authLoading || loading) {
    return (
      <AdminLayout>
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading booking details...</p>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className={styles.errorContainer}>
          <h2>Error Loading Booking</h2>
          <p>{error}</p>
          <Link href="/admin/bookings" className={styles.backButton}>
            ← Back to Bookings
          </Link>
        </div>
      </AdminLayout>
    );
  }

  if (!booking) {
    return (
      <AdminLayout>
        <div className={styles.notFoundContainer}>
          <h2>Booking Not Found</h2>
          <p>The booking you're looking for doesn't exist.</p>
          <Link href="/admin/bookings" className={styles.backButton}>
            ← Back to Bookings
          </Link>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Head>
        <title>Booking #{booking.id} - Details | Ocean Soul Sparkles Admin</title>
        <meta name="description" content={`Details for booking #${booking.id}`} />
      </Head>

      <div className={styles.bookingDetailsContainer}>
        <header className={styles.header}>
          <div className={styles.breadcrumb}>
            <Link href="/admin/bookings">Bookings</Link>
            <span>/</span>
            <span>Booking #{booking.id}</span>
          </div>
          
          <div className={styles.headerActions}>
            <Link href={`/admin/bookings/${booking.id}/edit`} className={styles.editButton}>
              ✏️ Edit Booking
            </Link>
            <button onClick={handleDelete} className={styles.deleteButton}>
              🗑️ Delete
            </button>
            <Link href="/admin/bookings" className={styles.backButton}>
              ← Back to Bookings
            </Link>
          </div>
        </header>

        <div className={styles.bookingContent}>
          <div className={styles.mainInfo}>
            <div className={styles.bookingHeader}>
              <h1>Booking #{booking.id}</h1>
              <div className={styles.statusSection}>
                <span 
                  className={styles.statusBadge}
                  style={{ backgroundColor: getStatusColor(booking.status) }}
                >
                  {booking.status}
                </span>
                <select
                  value={booking.status}
                  onChange={(e) => updateBookingStatus(e.target.value)}
                  className={styles.statusSelect}
                >
                  <option value="pending">Pending</option>
                  <option value="confirmed">Confirmed</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                  <option value="no_show">No Show</option>
                </select>
              </div>
            </div>

            <div className={styles.detailsGrid}>
              <div className={styles.detailCard}>
                <h4>Customer Information</h4>
                <div className={styles.customerInfo}>
                  <div className={styles.customerName}>
                    <strong>{booking.customer_name}</strong>
                  </div>
                  <div className={styles.contactInfo}>
                    <div>📧 {booking.customer_email}</div>
                    <div>📞 {booking.customer_phone}</div>
                  </div>
                  <Link href={`/admin/customers/${booking.customers?.id}`} className={styles.customerLink}>
                    View Customer Profile →
                  </Link>
                </div>
              </div>

              <div className={styles.detailCard}>
                <h4>Service Details</h4>
                <div className={styles.serviceInfo}>
                  <div className={styles.serviceName}>
                    <strong>{booking.service_name}</strong>
                  </div>
                  <div className={styles.serviceDetails}>
                    <div>Duration: {formatDuration(booking.service_duration)}</div>
                    <div>Price: {formatCurrency(booking.service_price)}</div>
                  </div>
                  <Link href={`/admin/services/${booking.services?.id}`} className={styles.serviceLink}>
                    View Service Details →
                  </Link>
                </div>
              </div>

              <div className={styles.detailCard}>
                <h4>Appointment Details</h4>
                <div className={styles.appointmentInfo}>
                  <div className={styles.dateTime}>
                    <div><strong>Date:</strong> {formatDate(booking.booking_date)}</div>
                    <div><strong>Time:</strong> {booking.booking_time}</div>
                  </div>
                  <div className={styles.artistInfo}>
                    <div><strong>Artist:</strong> {booking.artist_name}</div>
                  </div>
                </div>
              </div>

              <div className={styles.detailCard}>
                <h4>Payment Information</h4>
                <div className={styles.paymentInfo}>
                  <div className={styles.amount}>
                    <strong>Total Amount: {formatCurrency(booking.total_amount)}</strong>
                  </div>
                  <div className={styles.paymentStatus}>
                    Payment Status: <span className={styles.paid}>Paid</span>
                  </div>
                </div>
              </div>
            </div>

            {booking.notes && (
              <div className={styles.notesSection}>
                <h4>Notes</h4>
                <p className={styles.notes}>{booking.notes}</p>
              </div>
            )}

            <div className={styles.metaInfo}>
              <div className={styles.metaItem}>
                <strong>Created:</strong> {formatDateTime(booking.created_at)}
              </div>
              <div className={styles.metaItem}>
                <strong>Booking ID:</strong> {booking.id}
              </div>
            </div>
          </div>

          <div className={styles.sidebar}>
            <div className={styles.quickActions}>
              <h3>Quick Actions</h3>
              <Link href={`/admin/bookings/${booking.id}/edit`} className={styles.actionButton}>
                Edit Booking Details
              </Link>
              <Link href={`/admin/customers/${booking.customers?.id}`} className={styles.actionButton}>
                View Customer Profile
              </Link>
              <Link href={`/admin/bookings/new?customer=${booking.customers?.id}`} className={styles.actionButton}>
                Book Another Service
              </Link>
            </div>

            <div className={styles.timeline}>
              <h3>Booking Timeline</h3>
              <div className={styles.timelineItem}>
                <div className={styles.timelineDate}>{formatDateTime(booking.created_at)}</div>
                <div className={styles.timelineEvent}>Booking created</div>
              </div>
              <div className={styles.timelineItem}>
                <div className={styles.timelineDate}>{formatDate(booking.booking_date)} {booking.booking_time}</div>
                <div className={styles.timelineEvent}>Scheduled appointment</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
