/**
 * Ocean Soul Sparkles Admin - Keyboard Shortcuts Styles
 * Modal interface for displaying keyboard shortcuts help
 */

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal {
  background: var(--admin-bg-primary, #ffffff);
  border-radius: var(--admin-radius-lg, 12px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid var(--admin-border-light, #e0e0e0);
  background: var(--admin-bg-secondary, #f8f9fa);
}

.title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--admin-text-primary, #1a1a1a);
}

.closeBtn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--admin-text-secondary, #666666);
  cursor: pointer;
  padding: 4px;
  border-radius: var(--admin-radius-sm, 4px);
  transition: all var(--admin-transition-normal, 0.2s ease);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.closeBtn:hover {
  background: var(--admin-bg-primary, #ffffff);
  color: var(--admin-text-primary, #1a1a1a);
  transform: scale(1.1);
}

.content {
  padding: 24px;
  overflow-y: auto;
  max-height: calc(90vh - 80px);
}

.intro {
  margin-bottom: 24px;
  padding: 16px;
  background: var(--admin-primary-light, #e3f2fd);
  border-radius: var(--admin-radius-md, 8px);
  border-left: 4px solid var(--admin-primary, #3788d8);
}

.intro p {
  margin: 0;
  color: var(--admin-text-primary, #1a1a1a);
  font-size: 0.95rem;
  line-height: 1.5;
}

.shortcutsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.category {
  background: var(--admin-bg-primary, #ffffff);
  border: 1px solid var(--admin-border-light, #e0e0e0);
  border-radius: var(--admin-radius-md, 8px);
  overflow: hidden;
}

.categoryTitle {
  margin: 0;
  padding: 16px 20px;
  background: var(--admin-bg-secondary, #f8f9fa);
  border-bottom: 1px solid var(--admin-border-light, #e0e0e0);
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--admin-text-primary, #1a1a1a);
}

.shortcutsList {
  padding: 0;
}

.shortcutItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  border-bottom: 1px solid var(--admin-border-light, #e0e0e0);
  transition: background var(--admin-transition-normal, 0.2s ease);
}

.shortcutItem:last-child {
  border-bottom: none;
}

.shortcutItem:hover {
  background: var(--admin-bg-secondary, #f8f9fa);
}

.keys {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
}

.key {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  height: 24px;
  padding: 0 6px;
  background: var(--admin-bg-primary, #ffffff);
  border: 1px solid var(--admin-border-medium, #cccccc);
  border-radius: var(--admin-radius-sm, 4px);
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--admin-text-primary, #1a1a1a);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  text-align: center;
  line-height: 1;
}

.keySeparator {
  font-size: 0.8rem;
  color: var(--admin-text-secondary, #666666);
  margin: 0 2px;
}

.description {
  flex: 1;
  margin-left: 16px;
  font-size: 0.9rem;
  color: var(--admin-text-primary, #1a1a1a);
  line-height: 1.4;
}

.footer {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid var(--admin-border-light, #e0e0e0);
}

.tip {
  padding: 16px;
  background: var(--admin-warning-light, #fff3cd);
  border: 1px solid var(--admin-warning, #ffc107);
  border-radius: var(--admin-radius-md, 8px);
  font-size: 0.9rem;
  line-height: 1.5;
  color: var(--admin-text-primary, #1a1a1a);
}

.tip strong {
  color: var(--admin-warning-dark, #856404);
}

.tip .key {
  margin: 0 2px;
  font-size: 0.75rem;
  min-width: 20px;
  height: 20px;
}

.helpButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--admin-primary, #3788d8);
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all var(--admin-transition-normal, 0.2s ease);
  box-shadow: 0 2px 8px rgba(55, 136, 216, 0.3);
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

.helpButton:hover {
  background: var(--admin-primary-dark, #2c6bb8);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(55, 136, 216, 0.4);
}

.helpButton:active {
  transform: translateY(0);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .overlay {
    padding: 10px;
  }

  .modal {
    max-height: 95vh;
  }

  .header {
    padding: 16px 20px;
  }

  .title {
    font-size: 1.3rem;
  }

  .content {
    padding: 20px;
    max-height: calc(95vh - 70px);
  }

  .shortcutsGrid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .shortcutItem {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    padding: 16px 20px;
  }

  .keys {
    align-self: flex-end;
  }

  .description {
    margin-left: 0;
    font-size: 1rem;
  }

  .helpButton {
    width: 50px;
    height: 50px;
    bottom: 16px;
    right: 16px;
    font-size: 1.4rem;
  }
}

@media (max-width: 480px) {
  .overlay {
    padding: 5px;
  }

  .header {
    padding: 12px 16px;
  }

  .content {
    padding: 16px;
  }

  .categoryTitle {
    padding: 12px 16px;
    font-size: 1rem;
  }

  .shortcutItem {
    padding: 12px 16px;
  }

  .key {
    min-width: 28px;
    height: 28px;
    font-size: 0.9rem;
  }

  .description {
    font-size: 0.95rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .modal {
    background: var(--admin-bg-primary-dark, #1a1a1a);
  }

  .header {
    background: var(--admin-bg-secondary-dark, #2a2a2a);
    border-color: var(--admin-border-dark, #404040);
  }

  .category {
    background: var(--admin-bg-primary-dark, #1a1a1a);
    border-color: var(--admin-border-dark, #404040);
  }

  .categoryTitle {
    background: var(--admin-bg-secondary-dark, #2a2a2a);
    border-color: var(--admin-border-dark, #404040);
  }

  .shortcutItem {
    border-color: var(--admin-border-dark, #404040);
  }

  .shortcutItem:hover {
    background: var(--admin-bg-secondary-dark, #2a2a2a);
  }

  .key {
    background: var(--admin-bg-secondary-dark, #2a2a2a);
    border-color: var(--admin-border-dark, #404040);
    color: var(--admin-text-primary-dark, #ffffff);
  }

  .footer {
    border-color: var(--admin-border-dark, #404040);
  }

  .intro {
    background: rgba(55, 136, 216, 0.2);
  }

  .tip {
    background: rgba(255, 193, 7, 0.2);
    border-color: var(--admin-warning, #ffc107);
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .modal {
    border: 2px solid var(--admin-text-primary, #1a1a1a);
  }

  .key {
    border-width: 2px;
  }

  .category {
    border-width: 2px;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .overlay,
  .modal {
    animation: none;
  }

  .helpButton,
  .closeBtn,
  .shortcutItem {
    transition: none;
  }
}

/* Focus styles for accessibility */
.closeBtn:focus-visible {
  outline: 2px solid var(--admin-primary, #3788d8);
  outline-offset: 2px;
}

.helpButton:focus-visible {
  outline: 2px solid white;
  outline-offset: 2px;
}
