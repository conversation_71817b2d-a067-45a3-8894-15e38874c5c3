/* Staff Performance Page Styles */

.performanceContainer {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #e5e7eb;
}

.headerLeft {
  flex: 1;
}

.title {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.subtitle {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

.headerActions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.backBtn {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.backBtn:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.errorMessage {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.closeError {
  background: none;
  border: none;
  color: #dc2626;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dateRangeSection {
  margin-bottom: 32px;
  padding: 20px;
  background: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.dateInputs {
  display: flex;
  gap: 24px;
  align-items: center;
}

.inputGroup {
  display: flex;
  align-items: center;
  gap: 8px;
}

.inputGroup label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.dateInput {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
}

.summarySection {
  margin-bottom: 40px;
}

.summaryGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.summaryCard {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
  display: flex;
  align-items: center;
  gap: 16px;
}

.summaryCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.cardIcon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border-radius: 12px;
}

.cardContent {
  flex: 1;
}

.cardContent h3 {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  margin: 0 0 4px 0;
  text-transform: uppercase;
}

.cardValue {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 4px 0;
}

.cardSubtext {
  font-size: 12px;
  color: #6b7280;
}

.metricsSection {
  margin-bottom: 32px;
}

.sectionTitle {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 24px 0;
}

.emptyState {
  text-align: center;
  padding: 80px 20px;
  background: #f9fafb;
  border: 2px dashed #d1d5db;
  border-radius: 12px;
}

.emptyIcon {
  font-size: 48px;
  margin-bottom: 16px;
}

.emptyState h3 {
  font-size: 24px;
  color: #374151;
  margin: 0 0 8px 0;
}

.emptyState p {
  color: #6b7280;
  margin: 0;
  font-size: 16px;
}

.metricsTable {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tableContainer {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th {
  background: #f9fafb;
  padding: 16px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
  border-bottom: 1px solid #e5e7eb;
}

.tableRow {
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s ease;
}

.tableRow:hover {
  background: #f9fafb;
}

.table td {
  padding: 16px;
  vertical-align: middle;
  font-size: 14px;
}

.dateCell {
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
}

.numberCell {
  text-align: center;
  color: #1f2937;
  font-weight: 500;
}

.percentage {
  display: block;
  font-size: 12px;
  color: #6b7280;
  font-weight: 400;
}

.currencyCell {
  text-align: right;
  color: #1f2937;
  font-weight: 500;
  font-family: monospace;
}

.ratingCell {
  text-align: center;
  font-weight: 600;
}

.percentageCell {
  text-align: center;
  font-weight: 600;
}

.noData {
  color: #9ca3af;
  font-style: italic;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #6b7280;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .headerActions {
    justify-content: flex-start;
  }

  .summaryGrid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .performanceContainer {
    padding: 16px;
  }

  .dateInputs {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .summaryGrid {
    grid-template-columns: 1fr;
  }

  .summaryCard {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .cardIcon {
    width: 50px;
    height: 50px;
    font-size: 24px;
  }

  .cardValue {
    font-size: 24px;
  }

  .table th,
  .table td {
    padding: 12px 8px;
    font-size: 13px;
  }

  .percentage {
    display: inline;
    margin-left: 4px;
  }
}

@media (max-width: 640px) {
  .table {
    font-size: 12px;
  }

  .table th,
  .table td {
    padding: 8px 4px;
  }

  .cardContent h3 {
    font-size: 12px;
  }

  .cardValue {
    font-size: 20px;
  }

  .cardSubtext {
    font-size: 11px;
  }
}
