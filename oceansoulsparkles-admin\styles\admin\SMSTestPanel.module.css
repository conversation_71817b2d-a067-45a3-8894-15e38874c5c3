/* SMS Test Panel Styles */

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.panel {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e5e9;
}

.header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.closeBtn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  padding: 0.5rem;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.closeBtn:hover {
  background: #e9ecef;
  color: #333;
}

.content {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

.section {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e1e5e9;
}

.section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.section h3 {
  margin: 0 0 1rem 0;
  color: #2c3e50;
  font-size: 1.2rem;
}

.formGroup {
  margin-bottom: 1rem;
}

.formGroup label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #2c3e50;
}

.formGroup input,
.formGroup textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.formGroup input:focus,
.formGroup textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.formGroup small {
  display: block;
  margin-top: 0.25rem;
  color: #666;
  font-size: 0.875rem;
}

.statusBtn {
  background: #17a2b8;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.statusBtn:hover {
  background: #138496;
}

.statusInfo {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e1e5e9;
}

.statusInfo p {
  margin: 0.5rem 0;
  color: #555;
}

.error {
  color: #dc3545 !important;
}

.sendBtn {
  background: #28a745;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sendBtn:hover:not(:disabled) {
  background: #218838;
}

.sendBtn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.templateTests {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 1rem;
}

.templateBtn {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.templateBtn:hover:not(:disabled) {
  background: #5a6fd8;
}

.templateBtn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.result {
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid;
  margin-top: 1rem;
}

.result.success {
  background: #d4edda;
  border-color: #c3e6cb;
  color: #155724;
}

.result.error {
  background: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.result p {
  margin: 0.5rem 0;
}

.instructions {
  margin: 0;
  padding-left: 1.5rem;
  color: #555;
}

.instructions li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .panel {
    width: 95%;
    margin: 1rem;
  }
  
  .header {
    padding: 1rem;
  }
  
  .content {
    padding: 1rem;
  }
  
  .templateTests {
    flex-direction: column;
  }
  
  .templateBtn {
    width: 100%;
  }
}
