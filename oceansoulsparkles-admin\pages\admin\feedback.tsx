import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useAuth } from '../../hooks/useAuth';
import AdminLayout from '../../components/admin/AdminLayout';
import styles from '../../styles/admin/Feedback.module.css';

interface Feedback {
  id: string;
  customer_id: string;
  booking_id: string;
  artist_id?: string;
  rating: number;
  service_rating?: number;
  cleanliness_rating?: number;
  timeliness_rating?: number;
  overall_experience_rating?: number;
  feedback_text?: string;
  would_recommend?: boolean;
  improvement_suggestions?: string;
  is_public: boolean;
  response_text?: string;
  responded_at?: string;
  created_at: string;
  customers: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  bookings?: {
    id: string;
    start_time: string;
    services: {
      name: string;
    };
  };
  artist_profiles?: {
    id: string;
    name: string;
    artist_name: string;
  };
}

interface FeedbackAverages {
  overall: number;
  service: number;
  cleanliness: number;
  timeliness: number;
  experience: number;
}

export default function FeedbackPage() {
  const { user } = useAuth();
  const [feedback, setFeedback] = useState<Feedback[]>([]);
  const [averages, setAverages] = useState<FeedbackAverages>({
    overall: 0,
    service: 0,
    cleanliness: 0,
    timeliness: 0,
    experience: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedRating, setSelectedRating] = useState('all');
  const [showPublicOnly, setShowPublicOnly] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [limit] = useState(20);

  useEffect(() => {
    loadFeedback();
  }, [selectedRating, showPublicOnly, currentPage]);

  const loadFeedback = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (selectedRating !== 'all') {
        params.append('rating_min', selectedRating);
        params.append('rating_max', selectedRating);
      }
      if (showPublicOnly) params.append('is_public', 'true');
      params.append('limit', limit.toString());
      params.append('offset', ((currentPage - 1) * limit).toString());

      const response = await fetch(`/api/admin/feedback?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setFeedback(data.feedback || []);
        setAverages(data.averages || {});
        setTotalCount(data.total || 0);
      } else {
        setError('Failed to load feedback');
      }
    } catch (error) {
      console.error('Error loading feedback:', error);
      setError('Failed to load feedback');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-AU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <span key={i} className={i < rating ? styles.starFilled : styles.starEmpty}>
        ★
      </span>
    ));
  };

  const getRatingColor = (rating: number) => {
    if (rating >= 4.5) return '#10b981';
    if (rating >= 4) return '#84cc16';
    if (rating >= 3) return '#f59e0b';
    if (rating >= 2) return '#f97316';
    return '#ef4444';
  };

  const totalPages = Math.ceil(totalCount / limit);

  if (loading && feedback.length === 0) {
    return (
      <AdminLayout>
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading feedback...</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Head>
        <title>Customer Feedback | Ocean Soul Sparkles Admin</title>
        <meta name="description" content="View and manage customer feedback and reviews" />
      </Head>

      <div className={styles.feedbackContainer}>
        <header className={styles.header}>
          <div className={styles.headerLeft}>
            <h1 className={styles.title}>Customer Feedback</h1>
            <p className={styles.subtitle}>View and manage customer reviews and feedback</p>
          </div>
        </header>

        {error && (
          <div className={styles.errorMessage}>
            {error}
            <button onClick={() => setError(null)} className={styles.closeError}>×</button>
          </div>
        )}

        <div className={styles.averagesSection}>
          <h2 className={styles.averagesTitle}>Average Ratings</h2>
          <div className={styles.averagesGrid}>
            <div className={styles.averageCard}>
              <div className={styles.averageValue} style={{ color: getRatingColor(averages.overall) }}>
                {averages.overall.toFixed(1)}
              </div>
              <div className={styles.averageStars}>
                {renderStars(Math.round(averages.overall))}
              </div>
              <div className={styles.averageLabel}>Overall Rating</div>
            </div>
            <div className={styles.averageCard}>
              <div className={styles.averageValue} style={{ color: getRatingColor(averages.service) }}>
                {averages.service.toFixed(1)}
              </div>
              <div className={styles.averageStars}>
                {renderStars(Math.round(averages.service))}
              </div>
              <div className={styles.averageLabel}>Service Quality</div>
            </div>
            <div className={styles.averageCard}>
              <div className={styles.averageValue} style={{ color: getRatingColor(averages.cleanliness) }}>
                {averages.cleanliness.toFixed(1)}
              </div>
              <div className={styles.averageStars}>
                {renderStars(Math.round(averages.cleanliness))}
              </div>
              <div className={styles.averageLabel}>Cleanliness</div>
            </div>
            <div className={styles.averageCard}>
              <div className={styles.averageValue} style={{ color: getRatingColor(averages.timeliness) }}>
                {averages.timeliness.toFixed(1)}
              </div>
              <div className={styles.averageStars}>
                {renderStars(Math.round(averages.timeliness))}
              </div>
              <div className={styles.averageLabel}>Timeliness</div>
            </div>
            <div className={styles.averageCard}>
              <div className={styles.averageValue} style={{ color: getRatingColor(averages.experience) }}>
                {averages.experience.toFixed(1)}
              </div>
              <div className={styles.averageStars}>
                {renderStars(Math.round(averages.experience))}
              </div>
              <div className={styles.averageLabel}>Overall Experience</div>
            </div>
          </div>
        </div>

        <div className={styles.filters}>
          <div className={styles.filterGroup}>
            <label htmlFor="ratingFilter">Filter by Rating:</label>
            <select
              id="ratingFilter"
              value={selectedRating}
              onChange={(e) => {
                setSelectedRating(e.target.value);
                setCurrentPage(1);
              }}
              className={styles.filterSelect}
            >
              <option value="all">All Ratings</option>
              <option value="5">5 Stars</option>
              <option value="4">4 Stars</option>
              <option value="3">3 Stars</option>
              <option value="2">2 Stars</option>
              <option value="1">1 Star</option>
            </select>
          </div>

          <div className={styles.filterGroup}>
            <label className={styles.checkboxLabel}>
              <input
                type="checkbox"
                checked={showPublicOnly}
                onChange={(e) => {
                  setShowPublicOnly(e.target.checked);
                  setCurrentPage(1);
                }}
              />
              Show public reviews only
            </label>
          </div>
        </div>

        <div className={styles.feedbackList}>
          {feedback.length === 0 ? (
            <div className={styles.emptyState}>
              <h3>No feedback found</h3>
              <p>No customer feedback matches your current filters.</p>
            </div>
          ) : (
            feedback.map((item) => (
              <div key={item.id} className={styles.feedbackCard}>
                <div className={styles.cardHeader}>
                  <div className={styles.customerInfo}>
                    <h3 className={styles.customerName}>
                      {item.customers.first_name} {item.customers.last_name}
                    </h3>
                    {item.bookings && (
                      <p className={styles.bookingInfo}>
                        {item.bookings.services.name} - {formatDate(item.bookings.start_time)}
                      </p>
                    )}
                    {item.artist_profiles && (
                      <p className={styles.artistInfo}>
                        Artist: {item.artist_profiles.name || item.artist_profiles.artist_name}
                      </p>
                    )}
                  </div>
                  <div className={styles.cardMeta}>
                    <div className={styles.overallRating}>
                      <span className={styles.ratingValue} style={{ color: getRatingColor(item.rating) }}>
                        {item.rating}
                      </span>
                      <div className={styles.ratingStars}>
                        {renderStars(item.rating)}
                      </div>
                    </div>
                    <div className={styles.feedbackDate}>
                      {formatDate(item.created_at)}
                    </div>
                    {item.is_public && (
                      <span className={styles.publicBadge}>Public</span>
                    )}
                  </div>
                </div>

                {(item.service_rating || item.cleanliness_rating || item.timeliness_rating || item.overall_experience_rating) && (
                  <div className={styles.detailedRatings}>
                    {item.service_rating && (
                      <div className={styles.ratingDetail}>
                        <span>Service: </span>
                        {renderStars(item.service_rating)}
                        <span className={styles.ratingNumber}>({item.service_rating})</span>
                      </div>
                    )}
                    {item.cleanliness_rating && (
                      <div className={styles.ratingDetail}>
                        <span>Cleanliness: </span>
                        {renderStars(item.cleanliness_rating)}
                        <span className={styles.ratingNumber}>({item.cleanliness_rating})</span>
                      </div>
                    )}
                    {item.timeliness_rating && (
                      <div className={styles.ratingDetail}>
                        <span>Timeliness: </span>
                        {renderStars(item.timeliness_rating)}
                        <span className={styles.ratingNumber}>({item.timeliness_rating})</span>
                      </div>
                    )}
                    {item.overall_experience_rating && (
                      <div className={styles.ratingDetail}>
                        <span>Experience: </span>
                        {renderStars(item.overall_experience_rating)}
                        <span className={styles.ratingNumber}>({item.overall_experience_rating})</span>
                      </div>
                    )}
                  </div>
                )}

                {item.feedback_text && (
                  <div className={styles.feedbackText}>
                    <h4>Feedback:</h4>
                    <p>{item.feedback_text}</p>
                  </div>
                )}

                {item.improvement_suggestions && (
                  <div className={styles.suggestions}>
                    <h4>Suggestions for Improvement:</h4>
                    <p>{item.improvement_suggestions}</p>
                  </div>
                )}

                {item.would_recommend !== null && (
                  <div className={styles.recommendation}>
                    <strong>Would recommend: </strong>
                    <span className={item.would_recommend ? styles.recommendYes : styles.recommendNo}>
                      {item.would_recommend ? 'Yes' : 'No'}
                    </span>
                  </div>
                )}

                {item.response_text && (
                  <div className={styles.adminResponse}>
                    <h4>Admin Response:</h4>
                    <p>{item.response_text}</p>
                    <div className={styles.responseDate}>
                      Responded on {formatDate(item.responded_at!)}
                    </div>
                  </div>
                )}
              </div>
            ))
          )}
        </div>

        {totalPages > 1 && (
          <div className={styles.pagination}>
            <button
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
              className={styles.paginationBtn}
            >
              Previous
            </button>
            <span className={styles.paginationInfo}>
              Page {currentPage} of {totalPages} ({totalCount} total)
            </span>
            <button
              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              disabled={currentPage === totalPages}
              className={styles.paginationBtn}
            >
              Next
            </button>
          </div>
        )}
      </div>
    </AdminLayout>
  );
}
