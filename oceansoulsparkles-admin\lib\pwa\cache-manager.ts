/**
 * Ocean Soul Sparkles Admin - PWA Cache Manager
 * Manages offline data caching and synchronization
 */

interface OfflineTransaction {
  id: string;
  timestamp: number;
  type: 'pos' | 'booking' | 'customer';
  data: any;
  synced: boolean;
}

interface CacheConfig {
  dbName: string;
  version: number;
  stores: {
    transactions: string;
    bookings: string;
    customers: string;
    services: string;
    settings: string;
  };
}

class PWACacheManager {
  private db: IDBDatabase | null = null;
  private config: CacheConfig = {
    dbName: 'OSSAdminCache',
    version: 2,
    stores: {
      transactions: 'offline_transactions',
      bookings: 'offline_bookings',
      customers: 'offline_customers',
      services: 'offline_services',
      settings: 'cache_settings'
    }
  };

  /**
   * Initialize the cache manager and IndexedDB
   */
  async initialize(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.config.dbName, this.config.version);

      request.onerror = () => {
        console.error('Failed to open IndexedDB:', request.error);
        reject(request.error);
      };

      request.onsuccess = () => {
        this.db = request.result;
        console.log('PWA Cache Manager initialized');
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        // Create object stores
        if (!db.objectStoreNames.contains(this.config.stores.transactions)) {
          const transactionStore = db.createObjectStore(this.config.stores.transactions, { keyPath: 'id' });
          transactionStore.createIndex('timestamp', 'timestamp', { unique: false });
          transactionStore.createIndex('type', 'type', { unique: false });
          transactionStore.createIndex('synced', 'synced', { unique: false });
        }

        if (!db.objectStoreNames.contains(this.config.stores.bookings)) {
          const bookingStore = db.createObjectStore(this.config.stores.bookings, { keyPath: 'id' });
          bookingStore.createIndex('timestamp', 'timestamp', { unique: false });
          bookingStore.createIndex('synced', 'synced', { unique: false });
        }

        if (!db.objectStoreNames.contains(this.config.stores.customers)) {
          const customerStore = db.createObjectStore(this.config.stores.customers, { keyPath: 'id' });
          customerStore.createIndex('lastUpdated', 'lastUpdated', { unique: false });
        }

        if (!db.objectStoreNames.contains(this.config.stores.services)) {
          const servicesStore = db.createObjectStore(this.config.stores.services, { keyPath: 'id' });
          servicesStore.createIndex('lastUpdated', 'lastUpdated', { unique: false });
          servicesStore.createIndex('category', 'category', { unique: false });
        }

        if (!db.objectStoreNames.contains(this.config.stores.settings)) {
          db.createObjectStore(this.config.stores.settings, { keyPath: 'key' });
        }
      };
    });
  }

  /**
   * Store offline transaction for later sync
   */
  async storeOfflineTransaction(type: 'pos' | 'booking' | 'customer', data: any): Promise<string> {
    if (!this.db) {
      throw new Error('Cache manager not initialized');
    }

    const transaction: OfflineTransaction = {
      id: `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      type,
      data,
      synced: false
    };

    return new Promise((resolve, reject) => {
      const dbTransaction = this.db!.transaction([this.config.stores.transactions], 'readwrite');
      const store = dbTransaction.objectStore(this.config.stores.transactions);
      const request = store.add(transaction);

      request.onsuccess = () => {
        console.log('Offline transaction stored:', transaction.id);
        resolve(transaction.id);
      };

      request.onerror = () => {
        console.error('Failed to store offline transaction:', request.error);
        reject(request.error);
      };
    });
  }

  /**
   * Get all unsynced offline transactions
   */
  async getUnsyncedTransactions(): Promise<OfflineTransaction[]> {
    if (!this.db) {
      throw new Error('Cache manager not initialized');
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.config.stores.transactions], 'readonly');
      const store = transaction.objectStore(this.config.stores.transactions);
      const index = store.index('synced');
      const request = index.getAll(IDBKeyRange.only(false));

      request.onsuccess = () => {
        resolve(request.result);
      };

      request.onerror = () => {
        console.error('Failed to get unsynced transactions:', request.error);
        reject(request.error);
      };
    });
  }

  /**
   * Mark transaction as synced
   */
  async markTransactionSynced(id: string): Promise<void> {
    if (!this.db) {
      throw new Error('Cache manager not initialized');
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.config.stores.transactions], 'readwrite');
      const store = transaction.objectStore(this.config.stores.transactions);
      const getRequest = store.get(id);

      getRequest.onsuccess = () => {
        const record = getRequest.result;
        if (record) {
          record.synced = true;
          const updateRequest = store.put(record);
          
          updateRequest.onsuccess = () => {
            console.log('Transaction marked as synced:', id);
            resolve();
          };
          
          updateRequest.onerror = () => {
            reject(updateRequest.error);
          };
        } else {
          reject(new Error('Transaction not found'));
        }
      };

      getRequest.onerror = () => {
        reject(getRequest.error);
      };
    });
  }

  /**
   * Cache customer data for offline access
   */
  async cacheCustomerData(customers: any[]): Promise<void> {
    if (!this.db) {
      throw new Error('Cache manager not initialized');
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.config.stores.customers], 'readwrite');
      const store = transaction.objectStore(this.config.stores.customers);

      let completed = 0;
      const total = customers.length;

      if (total === 0) {
        resolve();
        return;
      }

      customers.forEach(customer => {
        const customerWithTimestamp = {
          ...customer,
          lastUpdated: Date.now()
        };

        const request = store.put(customerWithTimestamp);
        
        request.onsuccess = () => {
          completed++;
          if (completed === total) {
            console.log(`Cached ${total} customers for offline access`);
            resolve();
          }
        };

        request.onerror = () => {
          console.error('Failed to cache customer:', customer.id, request.error);
          reject(request.error);
        };
      });
    });
  }

  /**
   * Get cached customer data
   */
  async getCachedCustomers(): Promise<any[]> {
    if (!this.db) {
      throw new Error('Cache manager not initialized');
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.config.stores.customers], 'readonly');
      const store = transaction.objectStore(this.config.stores.customers);
      const request = store.getAll();

      request.onsuccess = () => {
        resolve(request.result);
      };

      request.onerror = () => {
        console.error('Failed to get cached customers:', request.error);
        reject(request.error);
      };
    });
  }

  /**
   * Cache services data for offline access
   */
  async cacheServicesData(services: any[]): Promise<void> {
    if (!this.db) {
      throw new Error('Cache manager not initialized');
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.config.stores.services], 'readwrite');
      const store = transaction.objectStore(this.config.stores.services);

      let completed = 0;
      const total = services.length;

      if (total === 0) {
        resolve();
        return;
      }

      services.forEach(service => {
        const serviceWithTimestamp = {
          ...service,
          lastUpdated: Date.now()
        };

        const request = store.put(serviceWithTimestamp);

        request.onsuccess = () => {
          completed++;
          if (completed === total) {
            console.log(`Cached ${total} services for offline access`);
            resolve();
          }
        };

        request.onerror = () => {
          console.error('Failed to cache service:', service.id, request.error);
          reject(request.error);
        };
      });
    });
  }

  /**
   * Get cached services data
   */
  async getCachedServices(): Promise<any[]> {
    if (!this.db) {
      throw new Error('Cache manager not initialized');
    }

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.config.stores.services], 'readonly');
      const store = transaction.objectStore(this.config.stores.services);
      const request = store.getAll();

      request.onsuccess = () => {
        resolve(request.result);
      };

      request.onerror = () => {
        console.error('Failed to get cached services:', request.error);
        reject(request.error);
      };
    });
  }

  /**
   * Check if device is online
   */
  isOnline(): boolean {
    return navigator.onLine;
  }

  /**
   * Register for background sync
   */
  async registerBackgroundSync(tag: string): Promise<void> {
    if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
      try {
        const registration = await navigator.serviceWorker.ready;
        // Use type assertion for sync property that may not be in TypeScript definitions
        await (registration as any).sync.register(tag);
        console.log('Background sync registered:', tag);
      } catch (error) {
        console.error('Failed to register background sync:', error);
      }
    }
  }

  /**
   * Clear old cached data
   */
  async clearOldCache(maxAge: number = 7 * 24 * 60 * 60 * 1000): Promise<void> {
    if (!this.db) {
      throw new Error('Cache manager not initialized');
    }

    const cutoffTime = Date.now() - maxAge;

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.config.stores.transactions], 'readwrite');
      const store = transaction.objectStore(this.config.stores.transactions);
      const index = store.index('timestamp');
      const range = IDBKeyRange.upperBound(cutoffTime);
      const request = index.openCursor(range);

      let deletedCount = 0;

      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result;
        if (cursor) {
          // Only delete synced transactions
          if (cursor.value.synced) {
            cursor.delete();
            deletedCount++;
          }
          cursor.continue();
        } else {
          console.log(`Cleared ${deletedCount} old cached transactions`);
          resolve();
        }
      };

      request.onerror = () => {
        console.error('Failed to clear old cache:', request.error);
        reject(request.error);
      };
    });
  }

  /**
   * Get cache statistics
   */
  async getCacheStats(): Promise<{
    totalTransactions: number;
    unsyncedTransactions: number;
    cachedCustomers: number;
    cacheSize: string;
  }> {
    if (!this.db) {
      throw new Error('Cache manager not initialized');
    }

    const [totalTransactions, unsyncedTransactions, cachedCustomers] = await Promise.all([
      this.getTransactionCount(),
      this.getUnsyncedTransactionCount(),
      this.getCachedCustomerCount()
    ]);

    // Estimate cache size (rough calculation)
    const estimatedSize = (totalTransactions * 1024) + (cachedCustomers * 512); // bytes
    const cacheSize = this.formatBytes(estimatedSize);

    return {
      totalTransactions,
      unsyncedTransactions,
      cachedCustomers,
      cacheSize
    };
  }

  private async getTransactionCount(): Promise<number> {
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.config.stores.transactions], 'readonly');
      const store = transaction.objectStore(this.config.stores.transactions);
      const request = store.count();

      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  private async getUnsyncedTransactionCount(): Promise<number> {
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.config.stores.transactions], 'readonly');
      const store = transaction.objectStore(this.config.stores.transactions);
      const index = store.index('synced');
      const request = index.count(IDBKeyRange.only(false));

      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  private async getCachedCustomerCount(): Promise<number> {
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([this.config.stores.customers], 'readonly');
      const store = transaction.objectStore(this.config.stores.customers);
      const request = store.count();

      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// Export singleton instance
export const cacheManager = new PWACacheManager();
export default cacheManager;
