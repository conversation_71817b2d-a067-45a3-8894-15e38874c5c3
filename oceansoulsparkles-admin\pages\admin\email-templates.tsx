import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useAuth } from '../../hooks/useAuth';
import AdminLayout from '../../components/admin/AdminLayout';
import styles from '../../styles/admin/EmailTemplates.module.css';

interface EmailTemplate {
  id: string;
  name: string;
  type: string;
  subject: string;
  html_content: string;
  text_content?: string;
  variables: string[];
  is_active: boolean;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

const EMAIL_TYPES = [
  { value: 'booking_confirmation', label: 'Booking Confirmation' },
  { value: 'booking_reminder', label: 'Booking Reminder' },
  { value: 'booking_cancellation', label: 'Booking Cancellation' },
  { value: 'payment_receipt', label: 'Payment Receipt' },
  { value: 'staff_notification', label: 'Staff Notification' },
  { value: 'custom', label: 'Custom' }
];

export default function EmailTemplatesPage() {
  const { user } = useAuth();
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedType, setSelectedType] = useState('all');
  const [showActiveOnly, setShowActiveOnly] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplate | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);

  useEffect(() => {
    loadTemplates();
  }, [selectedType, showActiveOnly]);

  const loadTemplates = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (selectedType !== 'all') params.append('type', selectedType);
      if (showActiveOnly) params.append('active_only', 'true');

      const response = await fetch(`/api/admin/email-templates?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setTemplates(data.templates || []);
      } else {
        setError('Failed to load email templates');
      }
    } catch (error) {
      console.error('Error loading templates:', error);
      setError('Failed to load email templates');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleActive = async (templateId: string, currentStatus: boolean) => {
    try {
      const template = templates.find(t => t.id === templateId);
      if (!template) return;

      const response = await fetch(`/api/admin/email-templates/${templateId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        },
        body: JSON.stringify({
          ...template,
          is_active: !currentStatus
        })
      });

      if (response.ok) {
        await loadTemplates();
      } else {
        setError('Failed to update template status');
      }
    } catch (error) {
      console.error('Error updating template:', error);
      setError('Failed to update template status');
    }
  };

  const handleSetDefault = async (templateId: string) => {
    try {
      const template = templates.find(t => t.id === templateId);
      if (!template) return;

      const response = await fetch(`/api/admin/email-templates/${templateId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        },
        body: JSON.stringify({
          ...template,
          is_default: true
        })
      });

      if (response.ok) {
        await loadTemplates();
      } else {
        setError('Failed to set default template');
      }
    } catch (error) {
      console.error('Error setting default:', error);
      setError('Failed to set default template');
    }
  };

  const handleDeleteTemplate = async (templateId: string) => {
    if (!confirm('Are you sure you want to delete this template? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/email-templates/${templateId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin-token')}`
        }
      });

      if (response.ok) {
        await loadTemplates();
      } else {
        const data = await response.json();
        setError(data.message || 'Failed to delete template');
      }
    } catch (error) {
      console.error('Error deleting template:', error);
      setError('Failed to delete template');
    }
  };

  const getTypeLabel = (type: string) => {
    const typeObj = EMAIL_TYPES.find(t => t.value === type);
    return typeObj ? typeObj.label : type;
  };

  const filteredTemplates = templates.filter(template => {
    if (selectedType !== 'all' && template.type !== selectedType) return false;
    if (showActiveOnly && !template.is_active) return false;
    return true;
  });

  if (loading) {
    return (
      <AdminLayout>
        <div className={styles.loadingContainer}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading email templates...</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Head>
        <title>Email Templates | Ocean Soul Sparkles Admin</title>
        <meta name="description" content="Manage email templates for customer communications" />
      </Head>

      <div className={styles.templatesContainer}>
        <header className={styles.header}>
          <div className={styles.headerLeft}>
            <h1 className={styles.title}>Email Templates</h1>
            <p className={styles.subtitle}>Manage email templates for customer communications</p>
          </div>
          <div className={styles.headerActions}>
            <button
              onClick={() => setShowCreateModal(true)}
              className={styles.createBtn}
            >
              + Create Template
            </button>
          </div>
        </header>

        {error && (
          <div className={styles.errorMessage}>
            {error}
            <button onClick={() => setError(null)} className={styles.closeError}>×</button>
          </div>
        )}

        <div className={styles.filters}>
          <div className={styles.filterGroup}>
            <label htmlFor="typeFilter">Filter by Type:</label>
            <select
              id="typeFilter"
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className={styles.filterSelect}
            >
              <option value="all">All Types</option>
              {EMAIL_TYPES.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          <div className={styles.filterGroup}>
            <label className={styles.checkboxLabel}>
              <input
                type="checkbox"
                checked={showActiveOnly}
                onChange={(e) => setShowActiveOnly(e.target.checked)}
              />
              Show active only
            </label>
          </div>
        </div>

        <div className={styles.templatesGrid}>
          {filteredTemplates.length === 0 ? (
            <div className={styles.emptyState}>
              <h3>No email templates found</h3>
              <p>Create your first email template to get started with customer communications.</p>
              <button
                onClick={() => setShowCreateModal(true)}
                className={styles.createBtn}
              >
                Create Template
              </button>
            </div>
          ) : (
            filteredTemplates.map((template) => (
              <div key={template.id} className={styles.templateCard}>
                <div className={styles.cardHeader}>
                  <div className={styles.templateInfo}>
                    <h3 className={styles.templateName}>{template.name}</h3>
                    <p className={styles.templateType}>{getTypeLabel(template.type)}</p>
                    <p className={styles.templateSubject}>{template.subject}</p>
                  </div>
                  <div className={styles.templateBadges}>
                    {template.is_default && (
                      <span className={styles.defaultBadge}>Default</span>
                    )}
                    <span 
                      className={`${styles.statusBadge} ${template.is_active ? styles.active : styles.inactive}`}
                    >
                      {template.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>

                <div className={styles.templateVariables}>
                  <strong>Variables:</strong> {template.variables.join(', ') || 'None'}
                </div>

                <div className={styles.cardActions}>
                  <button
                    onClick={() => {
                      setSelectedTemplate(template);
                      setShowPreviewModal(true);
                    }}
                    className={styles.previewBtn}
                  >
                    Preview
                  </button>
                  <button
                    onClick={() => setSelectedTemplate(template)}
                    className={styles.editBtn}
                  >
                    Edit
                  </button>
                  {!template.is_default && (
                    <button
                      onClick={() => handleSetDefault(template.id)}
                      className={styles.defaultBtn}
                    >
                      Set Default
                    </button>
                  )}
                  <button
                    onClick={() => handleToggleActive(template.id, template.is_active)}
                    className={`${styles.toggleBtn} ${template.is_active ? styles.deactivate : styles.activate}`}
                  >
                    {template.is_active ? 'Deactivate' : 'Activate'}
                  </button>
                  <button
                    onClick={() => handleDeleteTemplate(template.id)}
                    className={styles.deleteBtn}
                  >
                    Delete
                  </button>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </AdminLayout>
  );
}
