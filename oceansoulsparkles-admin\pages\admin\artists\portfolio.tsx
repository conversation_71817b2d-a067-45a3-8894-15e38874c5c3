import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import AdminLayout from '../../../components/admin/AdminLayout';
import PortfolioManager from '../../../components/admin/PortfolioManager';
import { useAuth } from '../../../hooks/useAuth';
import styles from '../../../styles/admin/Portfolio.module.css';

interface PortfolioStats {
  totalItems: number;
  featuredItems: number;
  publicItems: number;
  categories: string[];
  totalArtists: number;
}

export default function ArtistPortfolioPage() {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [stats, setStats] = useState<PortfolioStats>({
    totalItems: 0,
    featuredItems: 0,
    publicItems: 0,
    categories: [],
    totalArtists: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      loadPortfolioStats();
    }
  }, [user]);

  const loadPortfolioStats = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('adminToken');
      
      // Load portfolio statistics
      const portfolioResponse = await fetch('/api/admin/artists/portfolio', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!portfolioResponse.ok) {
        throw new Error('Failed to load portfolio statistics');
      }

      const portfolioData = await portfolioResponse.json();
      const items = portfolioData.portfolioItems || [];

      // Load artists count
      const artistsResponse = await fetch('/api/admin/artists', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      let totalArtists = 0;
      if (artistsResponse.ok) {
        const artistsData = await artistsResponse.json();
        totalArtists = artistsData.artists?.length || 0;
      }

      // Calculate statistics
      const categorySet = new Set(items.map((item: any) => item.category as string));
      const categories = Array.from(categorySet) as string[];
      const featuredItems = items.filter((item: any) => item.is_featured).length;
      const publicItems = items.filter((item: any) => item.is_public).length;

      setStats({
        totalItems: items.length,
        featuredItems,
        publicItems,
        categories,
        totalArtists
      });

    } catch (err) {
      console.error('Error loading portfolio stats:', err);
      setError('Failed to load portfolio statistics');
    } finally {
      setLoading(false);
    }
  };

  const handleItemAdded = () => {
    loadPortfolioStats();
  };

  const handleItemUpdated = () => {
    loadPortfolioStats();
  };

  const handleItemDeleted = () => {
    loadPortfolioStats();
  };

  if (authLoading || loading) {
    return (
      <AdminLayout>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>Loading portfolio management...</p>
        </div>
      </AdminLayout>
    );
  }

  if (!user) {
    router.push('/admin/login');
    return null;
  }

  // Check permissions
  if (!['DEV', 'Admin'].includes(user.role)) {
    return (
      <AdminLayout>
        <div className={styles.accessDenied}>
          <h2>Access Denied</h2>
          <p>You don't have permission to access portfolio management.</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <Head>
        <title>Artist Portfolio Management | Ocean Soul Sparkles Admin</title>
        <meta name="description" content="Manage artist portfolios and work samples" />
      </Head>

      <div className={styles.portfolioPage}>
        <div className={styles.pageHeader}>
          <div className={styles.headerContent}>
            <h1>Artist Portfolio Management</h1>
            <p>Manage artist portfolios, work samples, and showcase galleries</p>
          </div>
        </div>

        {error && (
          <div className={styles.error}>
            <p>{error}</p>
            <button onClick={() => setError(null)}>×</button>
          </div>
        )}

        {/* Portfolio Statistics */}
        <div className={styles.statsGrid}>
          <div className={styles.statCard}>
            <div className={styles.statIcon}>🎨</div>
            <div className={styles.statContent}>
              <h3>{stats.totalItems}</h3>
              <p>Total Portfolio Items</p>
            </div>
          </div>

          <div className={styles.statCard}>
            <div className={styles.statIcon}>⭐</div>
            <div className={styles.statContent}>
              <h3>{stats.featuredItems}</h3>
              <p>Featured Items</p>
            </div>
          </div>

          <div className={styles.statCard}>
            <div className={styles.statIcon}>👁️</div>
            <div className={styles.statContent}>
              <h3>{stats.publicItems}</h3>
              <p>Public Items</p>
            </div>
          </div>

          <div className={styles.statCard}>
            <div className={styles.statIcon}>👨‍🎨</div>
            <div className={styles.statContent}>
              <h3>{stats.totalArtists}</h3>
              <p>Active Artists</p>
            </div>
          </div>

          <div className={styles.statCard}>
            <div className={styles.statIcon}>📂</div>
            <div className={styles.statContent}>
              <h3>{stats.categories.length}</h3>
              <p>Categories</p>
            </div>
          </div>
        </div>

        {/* Categories Overview */}
        {stats.categories.length > 0 && (
          <div className={styles.categoriesOverview}>
            <h3>Portfolio Categories</h3>
            <div className={styles.categoryTags}>
              {stats.categories.map(category => (
                <span key={category} className={styles.categoryTag}>
                  {category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Portfolio Manager Component */}
        <PortfolioManager
          onItemAdded={handleItemAdded}
          onItemUpdated={handleItemUpdated}
          onItemDeleted={handleItemDeleted}
        />
      </div>
    </AdminLayout>
  );
}
