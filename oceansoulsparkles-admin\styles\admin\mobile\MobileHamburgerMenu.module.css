/**
 * Ocean Soul Sparkles Admin - Mobile Hamburger Menu Styles
 * Slide-out mobile menu for additional navigation options
 */

.menuOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.menuOverlay.open {
  opacity: 1;
  visibility: visible;
}

.menuContainer {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 280px;
  max-width: 80vw;
  background: var(--admin-card-background, #ffffff);
  box-shadow: 2px 0 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  transform: translateX(-100%);
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.menuOverlay.open .menuContainer {
  transform: translateX(0);
}

/* Header */
.menuHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid var(--admin-border, #e0e0e0);
  background: linear-gradient(135deg, #16213e 0%, #1a1a2e 100%);
  color: white;
}

.userInfo {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.userAvatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.userDetails {
  flex: 1;
}

.userName {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: white;
}

.userRole {
  font-size: 0.875rem;
  opacity: 0.9;
  color: rgba(255, 255, 255, 0.8);
}

.closeButton {
  background: none;
  border: none;
  color: white;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.1);
}

.closeButton:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.95);
}

/* Content */
.menuContent {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 16px 0;
}

.menuSection {
  margin-bottom: 24px;
}

.sectionTitle {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--admin-text-secondary, #666666);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0 0 8px 0;
  padding: 0 20px;
}

.sectionItems {
  display: flex;
  flex-direction: column;
}

.menuItem {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  text-decoration: none;
  color: var(--admin-text-primary, #1a1a1a);
  transition: all 0.2s ease;
  position: relative;
  min-height: 48px;
}

.menuItem:hover {
  background: var(--admin-hover-background, #f5f5f5);
}

.menuItem:active {
  background: var(--admin-active-background, #e0e0e0);
  transform: scale(0.98);
}

.menuItem.active {
  background: rgba(22, 33, 62, 0.1);
  color: var(--admin-primary, #16213e);
  font-weight: 500;
}

.menuItemIcon {
  font-size: 1.25rem;
  margin-right: 12px;
  width: 24px;
  text-align: center;
  flex-shrink: 0;
}

.menuItemLabel {
  flex: 1;
  font-size: 1rem;
}

.activeIndicator {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 24px;
  background: var(--admin-primary, #16213e);
  border-radius: 0 2px 2px 0;
}

/* Footer */
.menuFooter {
  padding: 16px 20px;
  border-top: 1px solid var(--admin-border, #e0e0e0);
  background: var(--admin-background, #f8f9fa);
}

.footerActions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 12px;
}

.footerLink {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  text-decoration: none;
  color: var(--admin-text-secondary, #666666);
  font-size: 0.875rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.footerLink:hover {
  background: var(--admin-hover, #f0f0f0);
  color: var(--admin-text-primary, #1a1a1a);
}

.footerLink span {
  font-size: 1rem;
}

.version {
  text-align: center;
  font-size: 0.75rem;
  color: var(--admin-text-secondary, #666666);
  opacity: 0.7;
  margin-top: 8px;
}

.menuItemBadge {
  background: var(--admin-primary, #16213e);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
}

.appInfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.appName {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--admin-text-primary, #1a1a1a);
}

.appVersion {
  font-size: 0.75rem;
  color: var(--admin-text-secondary, #666666);
}

/* Mobile-specific optimizations */
@media (max-width: 480px) {
  .menuContainer {
    width: 260px;
    max-width: 85vw;
  }

  .menuHeader {
    padding: 16px;
  }

  .userAvatar {
    width: 40px;
    height: 40px;
    font-size: 1.125rem;
  }

  .userName {
    font-size: 1rem;
  }

  .userRole {
    font-size: 0.8rem;
  }

  .menuItem {
    padding: 10px 16px;
    min-height: 44px;
  }

  .menuItemIcon {
    font-size: 1.125rem;
    margin-right: 10px;
    width: 20px;
  }

  .menuItemLabel {
    font-size: 0.9rem;
  }

  .sectionTitle {
    padding: 0 16px;
    font-size: 0.8rem;
  }

  .menuFooter {
    padding: 12px 16px;
  }
}

/* Landscape orientation adjustments */
@media (orientation: landscape) and (max-height: 500px) {
  .menuHeader {
    padding: 12px 16px;
  }

  .userAvatar {
    width: 36px;
    height: 36px;
    font-size: 1rem;
  }

  .userName {
    font-size: 0.9rem;
  }

  .userRole {
    font-size: 0.75rem;
  }

  .menuItem {
    padding: 8px 16px;
    min-height: 40px;
  }

  .menuSection {
    margin-bottom: 16px;
  }

  .menuFooter {
    padding: 8px 16px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .menuContainer {
    background: var(--admin-card-background-dark, #2a2a2a);
    box-shadow: 2px 0 12px rgba(0, 0, 0, 0.4);
  }

  .menuHeader {
    border-bottom-color: var(--admin-border-dark, #404040);
    background: linear-gradient(135deg, #0f1419 0%, #1a1a2e 100%);
  }

  .sectionTitle {
    color: var(--admin-text-secondary-dark, #cccccc);
  }

  .menuItem {
    color: var(--admin-text-primary-dark, #ffffff);
  }

  .menuItem:hover {
    background: var(--admin-hover-background-dark, #3a3a3a);
  }

  .menuItem:active {
    background: var(--admin-active-background-dark, #4a4a4a);
  }

  .menuItem.active {
    background: rgba(22, 33, 62, 0.3);
    color: var(--admin-primary, #16213e);
  }

  .menuFooter {
    border-top-color: var(--admin-border-dark, #404040);
    background: var(--admin-background-dark, #1a1a1a);
  }

  .appName {
    color: var(--admin-text-primary-dark, #ffffff);
  }

  .appVersion {
    color: var(--admin-text-secondary-dark, #cccccc);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .menuContainer {
    border-right: 2px solid var(--admin-border, #000000);
  }

  .menuHeader {
    border-bottom-width: 2px;
  }

  .menuItem.active {
    background: var(--admin-primary, #16213e);
    color: white;
  }

  .activeIndicator {
    width: 6px;
  }

  .menuFooter {
    border-top-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .menuOverlay {
    transition: opacity 0.1s ease, visibility 0.1s ease;
  }

  .menuContainer {
    transition: transform 0.1s ease;
  }

  .menuItem {
    transition: background-color 0.1s ease;
  }

  .menuItem:active {
    transform: none;
  }

  .closeButton:active {
    transform: none;
  }
}

/* Focus styles for accessibility */
.closeButton:focus,
.menuItem:focus {
  outline: 2px solid var(--admin-primary, #16213e);
  outline-offset: 2px;
}

.closeButton:focus:not(:focus-visible),
.menuItem:focus:not(:focus-visible) {
  outline: none;
}

/* Safe area support for devices with notches */
@supports (padding: max(0px)) {
  .menuHeader {
    padding-top: max(20px, env(safe-area-inset-top, 20px));
  }

  .menuFooter {
    padding-bottom: max(16px, env(safe-area-inset-bottom, 16px));
  }
}

/* Performance optimizations */
.menuContainer {
  will-change: transform;
  transform: translateZ(0);
}

.menuItem {
  will-change: background-color, transform;
  transform: translateZ(0);
}

/* Scrollbar styling */
.menuContent::-webkit-scrollbar {
  width: 4px;
}

.menuContent::-webkit-scrollbar-track {
  background: transparent;
}

.menuContent::-webkit-scrollbar-thumb {
  background: var(--admin-border, #e0e0e0);
  border-radius: 2px;
}

.menuContent::-webkit-scrollbar-thumb:hover {
  background: var(--admin-text-secondary, #666666);
}
